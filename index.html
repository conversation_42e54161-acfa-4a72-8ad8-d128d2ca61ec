<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Developer's Quest - Portfolio Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a2e;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Courier New', monospace;
            overflow: hidden;
        }
        
        #game-container {
            position: relative;
            border: 2px solid #16213e;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
        }
        
        #ui-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        }
        
        .hud {
            position: absolute;
            top: 20px;
            color: white;
            font-size: 16px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            pointer-events: none;
        }
        
        .hud-left {
            left: 20px;
        }
        
        .hud-right {
            right: 20px;
        }
        
        .project-modal {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #16213e, #1a1a2e);
            border: 3px solid #00ffff;
            border-radius: 12px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            color: white;
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
            display: none;
            pointer-events: auto;
            z-index: 2000;
        }
        
        .project-modal h2 {
            color: #00ffff;
            margin-top: 0;
            text-align: center;
            font-size: 24px;
        }
        
        .project-modal p {
            line-height: 1.6;
            margin: 15px 0;
        }
        
        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 15px 0;
        }
        
        .tech-tag {
            background: rgba(0, 255, 255, 0.2);
            border: 1px solid #00ffff;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .modal-button {
            background: linear-gradient(135deg, #00ffff, #0080ff);
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .modal-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 255, 255, 0.4);
        }
        
        .close-button {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: #00ffff;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .mobile-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: none;
            gap: 20px;
            pointer-events: auto;
        }
        
        .control-button {
            width: 60px;
            height: 60px;
            background: rgba(0, 255, 255, 0.3);
            border: 2px solid #00ffff;
            border-radius: 50%;
            color: white;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            user-select: none;
            transition: all 0.2s ease;
        }
        
        .control-button:active {
            background: rgba(0, 255, 255, 0.6);
            transform: scale(0.95);
        }
        
        @media (max-width: 768px) {
            .mobile-controls {
                display: flex;
            }
        }
        
        .loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #1a1a2e;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 3000;
        }
        
        .loading-text {
            font-size: 24px;
            margin-bottom: 20px;
            color: #00ffff;
        }
        
        .loading-bar {
            width: 300px;
            height: 6px;
            background: rgba(0, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
        }
        
        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #00ffff, #0080ff);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div id="ui-overlay">
            <!-- HUD Elements -->
            <div class="hud hud-left">
                <div>💰 Skill Coins: <span id="skill-coins">0</span></div>
            </div>
            <div class="hud hud-right">
                <div>💎 Project Crystals: <span id="project-crystals">0</span></div>
            </div>
            
            <!-- Project Detail Modal -->
            <div id="project-modal" class="project-modal">
                <button class="close-button" onclick="closeProjectModal()">&times;</button>
                <h2 id="project-title">Project Title</h2>
                <p id="project-description">Project description goes here...</p>
                <div class="tech-tags" id="project-tech"></div>
                <div class="modal-buttons">
                    <button class="modal-button" id="view-live-btn">View Live</button>
                    <button class="modal-button" id="view-code-btn">See Code</button>
                </div>
            </div>
            
            <!-- Mobile Controls -->
            <div class="mobile-controls">
                <div class="control-button" id="left-btn">←</div>
                <div class="control-button" id="jump-btn">↑</div>
                <div class="control-button" id="right-btn">→</div>
            </div>
        </div>
        
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-text">Loading The Developer's Quest...</div>
            <div class="loading-bar">
                <div class="loading-progress" id="loading-progress"></div>
            </div>
        </div>
    </div>

    <!-- Phaser 3 CDN -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <!-- Game Scripts -->
    <script src="js/portfolio-data.js"></script>
    <script src="js/game-config.js"></script>
    <script src="js/particle-effects.js"></script>
    <script src="js/scenes/preload-scene.js"></script>
    <script src="js/scenes/hub-scene.js"></script>
    <script src="js/scenes/frontend-scene.js"></script>
    <script src="js/scenes/backend-scene.js"></script>
    <script src="js/scenes/cloud-scene.js"></script>
    <script src="js/player.js"></script>
    <script src="js/collectibles.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
