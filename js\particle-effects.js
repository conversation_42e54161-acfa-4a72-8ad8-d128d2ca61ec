// Particle Effects Manager for The Developer's Quest
class ParticleEffectsManager {
    constructor(scene) {
        this.scene = scene;
        this.particles = [];
    }
    
    // Create explosion effect
    createExplosion(x, y, color = 0xffffff, particleCount = 12) {
        for (let i = 0; i < particleCount; i++) {
            const angle = (i / particleCount) * Math.PI * 2;
            const speed = Phaser.Math.Between(100, 200);
            const distance = Phaser.Math.Between(30, 80);
            
            const particle = this.scene.add.circle(x, y, Phaser.Math.Between(2, 6), color, 0.8);
            this.particles.push(particle);
            
            const targetX = x + Math.cos(angle) * distance;
            const targetY = y + Math.sin(angle) * distance;
            
            this.scene.tweens.add({
                targets: particle,
                x: targetX,
                y: targetY,
                scaleX: 0,
                scaleY: 0,
                alpha: 0,
                duration: Phaser.Math.Between(300, 600),
                ease: 'Power2',
                onComplete: () => {
                    particle.destroy();
                    this.removeParticle(particle);
                }
            });
        }
    }
    
    // Create sparkle effect
    createSparkles(x, y, count = 8, color = 0xffffff) {
        for (let i = 0; i < count; i++) {
            const offsetX = Phaser.Math.Between(-30, 30);
            const offsetY = Phaser.Math.Between(-30, 30);
            
            const sparkle = this.scene.add.circle(x + offsetX, y + offsetY, 2, color, 1);
            this.particles.push(sparkle);
            
            this.scene.tweens.add({
                targets: sparkle,
                scaleX: 0,
                scaleY: 0,
                alpha: 0,
                y: sparkle.y - 50,
                duration: 800,
                ease: 'Power2',
                onComplete: () => {
                    sparkle.destroy();
                    this.removeParticle(sparkle);
                }
            });
        }
    }
    
    // Create trail effect
    createTrail(x, y, color = 0x00ffff, size = 4) {
        const trail = this.scene.add.circle(x, y, size, color, 0.6);
        this.particles.push(trail);
        
        this.scene.tweens.add({
            targets: trail,
            scaleX: 0,
            scaleY: 0,
            alpha: 0,
            duration: 300,
            ease: 'Power2',
            onComplete: () => {
                trail.destroy();
                this.removeParticle(trail);
            }
        });
    }
    
    // Create floating text effect
    createFloatingText(x, y, text, color = '#ffffff', fontSize = '16px') {
        const textObj = this.scene.add.text(x, y, text, {
            fontSize: fontSize,
            fontFamily: 'Courier New',
            color: color,
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);
        
        this.particles.push(textObj);
        
        this.scene.tweens.add({
            targets: textObj,
            y: y - 50,
            alpha: 0,
            duration: 1000,
            ease: 'Power2',
            onComplete: () => {
                textObj.destroy();
                this.removeParticle(textObj);
            }
        });
    }
    
    // Create ripple effect
    createRipple(x, y, color = 0x00ffff, maxScale = 3) {
        const ripple = this.scene.add.circle(x, y, 10, color, 0.5);
        ripple.setStrokeStyle(3, color);
        ripple.setFillStyle();
        this.particles.push(ripple);
        
        this.scene.tweens.add({
            targets: ripple,
            scaleX: maxScale,
            scaleY: maxScale,
            alpha: 0,
            duration: 600,
            ease: 'Power2',
            onComplete: () => {
                ripple.destroy();
                this.removeParticle(ripple);
            }
        });
    }
    
    // Create dust cloud effect
    createDustCloud(x, y, color = 0xcccccc) {
        for (let i = 0; i < 6; i++) {
            const dust = this.scene.add.circle(
                x + Phaser.Math.Between(-20, 20),
                y + Phaser.Math.Between(-10, 10),
                Phaser.Math.Between(3, 8),
                color,
                0.4
            );
            this.particles.push(dust);
            
            this.scene.tweens.add({
                targets: dust,
                x: dust.x + Phaser.Math.Between(-30, 30),
                y: dust.y - Phaser.Math.Between(20, 40),
                scaleX: 0,
                scaleY: 0,
                alpha: 0,
                duration: Phaser.Math.Between(500, 800),
                ease: 'Power2',
                onComplete: () => {
                    dust.destroy();
                    this.removeParticle(dust);
                }
            });
        }
    }
    
    // Create energy beam effect
    createEnergyBeam(startX, startY, endX, endY, color = 0x00ffff) {
        const beam = this.scene.add.line(0, 0, startX, startY, endX, endY, color, 0.8);
        beam.setLineWidth(4);
        this.particles.push(beam);
        
        // Add glow effect
        const glow = this.scene.add.line(0, 0, startX, startY, endX, endY, color, 0.3);
        glow.setLineWidth(12);
        this.particles.push(glow);
        
        this.scene.tweens.add({
            targets: [beam, glow],
            alpha: 0,
            duration: 200,
            ease: 'Power2',
            onComplete: () => {
                beam.destroy();
                glow.destroy();
                this.removeParticle(beam);
                this.removeParticle(glow);
            }
        });
    }
    
    // Create screen shake effect
    createScreenShake(intensity = 10, duration = 200) {
        if (this.scene.cameras && this.scene.cameras.main) {
            this.scene.cameras.main.shake(duration, intensity);
        }
    }
    
    // Create collection burst effect
    createCollectionBurst(x, y, color = 0xffff00) {
        // Central flash
        const flash = this.scene.add.circle(x, y, 20, 0xffffff, 0.8);
        this.particles.push(flash);
        
        this.scene.tweens.add({
            targets: flash,
            scaleX: 3,
            scaleY: 3,
            alpha: 0,
            duration: 200,
            ease: 'Power2',
            onComplete: () => {
                flash.destroy();
                this.removeParticle(flash);
            }
        });
        
        // Radiating particles
        this.createExplosion(x, y, color, 8);
        
        // Sparkles
        this.createSparkles(x, y, 12, 0xffffff);
        
        // Screen shake
        this.createScreenShake(5, 150);
    }
    
    // Create portal effect
    createPortalEffect(x, y, radius = 50) {
        // Swirling particles
        for (let i = 0; i < 16; i++) {
            const angle = (i / 16) * Math.PI * 2;
            const startRadius = radius + 20;
            const startX = x + Math.cos(angle) * startRadius;
            const startY = y + Math.sin(angle) * startRadius;
            
            const particle = this.scene.add.circle(startX, startY, 3, 0x9400D3, 0.8);
            this.particles.push(particle);
            
            this.scene.tweens.add({
                targets: particle,
                x: x,
                y: y,
                scaleX: 0,
                scaleY: 0,
                alpha: 0,
                duration: 1000,
                ease: 'Power2',
                onComplete: () => {
                    particle.destroy();
                    this.removeParticle(particle);
                }
            });
        }
        
        // Central energy burst
        this.createRipple(x, y, 0x9400D3, 4);
    }
    
    // Remove particle from tracking array
    removeParticle(particle) {
        const index = this.particles.indexOf(particle);
        if (index > -1) {
            this.particles.splice(index, 1);
        }
    }
    
    // Clean up all particles
    destroy() {
        this.particles.forEach(particle => {
            if (particle && particle.destroy) {
                particle.destroy();
            }
        });
        this.particles = [];
    }
}

// Add to global scope for easy access
if (typeof window !== 'undefined') {
    window.ParticleEffectsManager = ParticleEffectsManager;
}
