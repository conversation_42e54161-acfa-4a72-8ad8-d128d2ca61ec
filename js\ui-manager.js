// UI Manager for The Developer's Quest
class UIManager {
    constructor() {
        this.isModalOpen = false;
        this.currentScene = null;
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Close modal when clicking outside
        document.addEventListener('click', (e) => {
            const modal = document.getElementById('project-modal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
        
        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isModalOpen) {
                this.closeModal();
            }
        });
        
        // Prevent context menu on mobile controls
        const mobileControls = document.querySelectorAll('.control-button');
        mobileControls.forEach(button => {
            button.addEventListener('contextmenu', (e) => {
                e.preventDefault();
            });
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }
    
    updateHUD(skillCoins, projectCrystals) {
        const skillCoinsElement = document.getElementById('skill-coins');
        const projectCrystalsElement = document.getElementById('project-crystals');
        
        if (skillCoinsElement) {
            // Add animation when count changes
            const currentCount = parseInt(skillCoinsElement.textContent);
            if (currentCount !== skillCoins) {
                skillCoinsElement.textContent = skillCoins;
                this.animateCounterUpdate(skillCoinsElement);
            }
        }
        
        if (projectCrystalsElement) {
            const currentCount = parseInt(projectCrystalsElement.textContent);
            if (currentCount !== projectCrystals) {
                projectCrystalsElement.textContent = projectCrystals;
                this.animateCounterUpdate(projectCrystalsElement);
            }
        }
    }
    
    animateCounterUpdate(element) {
        element.style.transform = 'scale(1.2)';
        element.style.color = '#00ffff';
        
        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.color = 'white';
        }, 200);
    }
    
    showProjectModal(projectData) {
        const modal = document.getElementById('project-modal');
        const title = document.getElementById('project-title');
        const description = document.getElementById('project-description');
        const techTags = document.getElementById('project-tech');
        const viewLiveBtn = document.getElementById('view-live-btn');
        const viewCodeBtn = document.getElementById('view-code-btn');
        
        if (!modal || !title || !description || !techTags) return;
        
        // Populate modal content
        title.textContent = projectData.title;
        description.textContent = projectData.description;
        
        // Create technology tags
        techTags.innerHTML = '';
        projectData.technologies.forEach(tech => {
            const tag = document.createElement('div');
            tag.className = 'tech-tag';
            tag.textContent = tech;
            techTags.appendChild(tag);
        });
        
        // Setup buttons
        if (viewLiveBtn) {
            viewLiveBtn.style.display = 'inline-block';
            viewLiveBtn.textContent = 'View Live';
            viewLiveBtn.onclick = () => {
                if (projectData.liveUrl && projectData.liveUrl !== '#') {
                    window.open(projectData.liveUrl, '_blank');
                }
            };
        }
        
        if (viewCodeBtn) {
            viewCodeBtn.style.display = 'inline-block';
            viewCodeBtn.textContent = 'See Code';
            viewCodeBtn.onclick = () => {
                if (projectData.codeUrl) {
                    window.open(projectData.codeUrl, '_blank');
                }
            };
        }
        
        // Show modal with animation
        modal.style.display = 'block';
        modal.style.opacity = '0';
        modal.style.transform = 'translate(-50%, -50%) scale(0.8)';
        
        setTimeout(() => {
            modal.style.transition = 'all 0.3s ease';
            modal.style.opacity = '1';
            modal.style.transform = 'translate(-50%, -50%) scale(1)';
        }, 10);
        
        this.isModalOpen = true;
        
        // Pause game if available
        if (window.game && this.currentScene) {
            window.game.scene.pause(this.currentScene);
        }
    }
    
    showAboutModal(developerData) {
        const modal = document.getElementById('project-modal');
        const title = document.getElementById('project-title');
        const description = document.getElementById('project-description');
        const techTags = document.getElementById('project-tech');
        const viewLiveBtn = document.getElementById('view-live-btn');
        const viewCodeBtn = document.getElementById('view-code-btn');
        
        if (!modal || !title || !description) return;
        
        title.textContent = developerData.name;
        description.textContent = developerData.bio;
        
        // Add role as a tag
        techTags.innerHTML = `<div class="tech-tag">${developerData.title}</div>`;
        
        // Hide action buttons
        if (viewLiveBtn) viewLiveBtn.style.display = 'none';
        if (viewCodeBtn) viewCodeBtn.style.display = 'none';
        
        // Show modal
        modal.style.display = 'block';
        modal.style.opacity = '0';
        modal.style.transform = 'translate(-50%, -50%) scale(0.8)';
        
        setTimeout(() => {
            modal.style.transition = 'all 0.3s ease';
            modal.style.opacity = '1';
            modal.style.transform = 'translate(-50%, -50%) scale(1)';
        }, 10);
        
        this.isModalOpen = true;
    }
    
    showContactModal(contactData) {
        const modal = document.getElementById('project-modal');
        const title = document.getElementById('project-title');
        const description = document.getElementById('project-description');
        const techTags = document.getElementById('project-tech');
        const viewLiveBtn = document.getElementById('view-live-btn');
        const viewCodeBtn = document.getElementById('view-code-btn');
        
        if (!modal || !title || !description) return;
        
        title.textContent = 'Get In Touch!';
        description.textContent = 'Thanks for playing through my portfolio! Ready to work together?';
        
        // Add contact info as tags
        techTags.innerHTML = `
            <div class="tech-tag">📧 ${contactData.email}</div>
            <div class="tech-tag">🐙 GitHub</div>
            <div class="tech-tag">💼 LinkedIn</div>
        `;
        
        // Setup contact buttons
        if (viewLiveBtn) {
            viewLiveBtn.textContent = 'LinkedIn';
            viewLiveBtn.style.display = 'inline-block';
            viewLiveBtn.onclick = () => window.open(contactData.linkedin, '_blank');
        }
        
        if (viewCodeBtn) {
            viewCodeBtn.textContent = 'GitHub';
            viewCodeBtn.style.display = 'inline-block';
            viewCodeBtn.onclick = () => window.open(contactData.github, '_blank');
        }
        
        // Show modal
        modal.style.display = 'block';
        this.isModalOpen = true;
    }
    
    closeModal() {
        const modal = document.getElementById('project-modal');
        if (!modal || !this.isModalOpen) return;
        
        modal.style.transition = 'all 0.3s ease';
        modal.style.opacity = '0';
        modal.style.transform = 'translate(-50%, -50%) scale(0.8)';
        
        setTimeout(() => {
            modal.style.display = 'none';
            modal.style.transition = '';
        }, 300);
        
        this.isModalOpen = false;
        
        // Resume game if available
        if (window.game && this.currentScene) {
            window.game.scene.resume(this.currentScene);
        }
    }
    
    setCurrentScene(sceneName) {
        this.currentScene = sceneName;
    }
    
    showPauseMenu() {
        // Create pause menu overlay
        const pauseOverlay = document.createElement('div');
        pauseOverlay.id = 'pause-overlay';
        pauseOverlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1500;
            color: white;
            font-family: 'Courier New', monospace;
        `;
        
        pauseOverlay.innerHTML = `
            <h2 style="color: #00ffff; margin-bottom: 30px;">Game Paused</h2>
            <button class="modal-button" onclick="uiManager.resumeGame()">Resume</button>
            <button class="modal-button" onclick="uiManager.restartLevel()" style="margin-top: 15px;">Restart Level</button>
            <button class="modal-button" onclick="uiManager.returnToHub()" style="margin-top: 15px;">Return to Hub</button>
        `;
        
        document.getElementById('ui-overlay').appendChild(pauseOverlay);
    }
    
    hidePauseMenu() {
        const pauseOverlay = document.getElementById('pause-overlay');
        if (pauseOverlay) {
            pauseOverlay.remove();
        }
    }
    
    resumeGame() {
        this.hidePauseMenu();
        if (window.game && this.currentScene) {
            window.game.scene.resume(this.currentScene);
        }
    }
    
    restartLevel() {
        this.hidePauseMenu();
        if (window.game && this.currentScene) {
            window.game.scene.restart(this.currentScene);
        }
    }
    
    returnToHub() {
        this.hidePauseMenu();
        if (window.game) {
            window.game.scene.start('HubScene');
        }
    }
    
    handleResize() {
        // Handle responsive design adjustments
        const gameContainer = document.getElementById('game-container');
        const mobileControls = document.querySelector('.mobile-controls');
        
        if (window.innerWidth <= 768) {
            if (mobileControls) {
                mobileControls.style.display = 'flex';
            }
        } else {
            if (mobileControls) {
                mobileControls.style.display = 'none';
            }
        }
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#00ff00' : '#00ffff'};
            color: black;
            padding: 10px 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            z-index: 2500;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        notification.textContent = message;
        
        document.getElementById('ui-overlay').appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);
        
        // Remove after delay
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }
}

// Create global UI manager instance
const uiManager = new UIManager();

// Global function to close modal (for backward compatibility)
function closeProjectModal() {
    uiManager.closeModal();
}
