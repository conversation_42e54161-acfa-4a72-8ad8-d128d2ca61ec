// Preload Scene for The Developer's Quest
class PreloadScene extends Phaser.Scene {
    constructor() {
        super({ key: 'PreloadScene' });
        this.loadingProgress = 0;
    }
    
    preload() {
        this.createLoadingScreen();
        this.setupLoadingEvents();
        this.loadAssets();
    }
    
    createLoadingScreen() {
        // Update HTML loading screen
        const loadingScreen = document.getElementById('loading-screen');
        const progressBar = document.getElementById('loading-progress');
        
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
        
        // Create in-game loading elements as backup
        const centerX = GAME_CONFIG.width / 2;
        const centerY = GAME_CONFIG.height / 2;
        
        this.add.text(centerX, centerY - 50, 'Loading The Developer\'s Quest...', {
            fontSize: '24px',
            fontFamily: 'Courier New',
            color: '#00ffff',
            align: 'center'
        }).setOrigin(0.5);
        
        // Loading bar background
        this.loadingBarBg = this.add.rectangle(centerX, centerY + 20, 400, 20, 0x333333);
        this.loadingBarBg.setStrokeStyle(2, 0x00ffff);
        
        // Loading bar fill
        this.loadingBar = this.add.rectangle(centerX - 200, centerY + 20, 0, 16, 0x00ffff);
        this.loadingBar.setOrigin(0, 0.5);
        
        // Loading percentage text
        this.loadingText = this.add.text(centerX, centerY + 60, '0%', {
            fontSize: '16px',
            fontFamily: 'Courier New',
            color: '#ffffff'
        }).setOrigin(0.5);
    }
    
    setupLoadingEvents() {
        // Update progress bar
        this.load.on('progress', (value) => {
            this.loadingProgress = value;
            this.updateLoadingDisplay(value);
        });
        
        // Handle individual file loads
        this.load.on('fileprogress', (file) => {
            console.log('Loading:', file.key);
        });
        
        // Handle loading completion
        this.load.on('complete', () => {
            this.updateLoadingDisplay(1);
            this.time.delayedCall(500, () => {
                this.hideLoadingScreen();
                this.scene.start('HubScene');
            });
        });
    }
    
    updateLoadingDisplay(progress) {
        const percentage = Math.round(progress * 100);
        
        // Update in-game loading bar
        if (this.loadingBar) {
            this.loadingBar.width = 400 * progress;
        }
        
        if (this.loadingText) {
            this.loadingText.setText(percentage + '%');
        }
        
        // Update HTML loading bar
        const progressBar = document.getElementById('loading-progress');
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }
    }
    
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
    }
    
    loadAssets() {
        // For now, we'll create placeholder assets programmatically
        // In a full implementation, you would load actual image and audio files
        
        // Create placeholder textures
        this.createPlaceholderTextures();
        
        // Load any actual assets (if they exist)
        // this.load.image('player-idle', 'assets/sprites/player-idle.png');
        // this.load.spritesheet('player-run', 'assets/sprites/player-run.png', { frameWidth: 32, frameHeight: 48 });
        // this.load.audio('jump', 'assets/audio/jump.wav');
        // this.load.audio('coin-collect', 'assets/audio/coin.wav');
        // this.load.audio('crystal-collect', 'assets/audio/crystal.wav');
        
        // Simulate loading time for demonstration
        this.load.image('dummy', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
        
        // Start loading
        this.load.start();
    }
    
    createPlaceholderTextures() {
        // Create enhanced pixel art style textures
        const graphics = this.add.graphics();

        // Player texture - more detailed character
        graphics.fillStyle(0x00ffff);
        graphics.fillRect(12, 0, 8, 8); // head
        graphics.fillStyle(0x0080ff);
        graphics.fillRect(10, 8, 12, 16); // body
        graphics.fillStyle(0x00ffff);
        graphics.fillRect(8, 12, 4, 8); // left arm
        graphics.fillRect(20, 12, 4, 8); // right arm
        graphics.fillStyle(0x004080);
        graphics.fillRect(12, 24, 3, 12); // left leg
        graphics.fillRect(17, 24, 3, 12); // right leg
        graphics.fillStyle(0xffffff);
        graphics.fillRect(14, 2, 2, 2); // eyes
        graphics.fillRect(16, 2, 2, 2);
        graphics.generateTexture('player', 32, 48);

        // Skill coin texture - more detailed coin
        graphics.clear();
        graphics.fillStyle(0xffd700);
        graphics.fillCircle(12, 12, 12);
        graphics.fillStyle(0xffff00);
        graphics.fillCircle(12, 12, 10);
        graphics.fillStyle(0xffd700);
        graphics.fillCircle(12, 12, 6);
        graphics.fillStyle(0xffff00);
        graphics.fillRect(8, 11, 8, 2);
        graphics.fillRect(11, 8, 2, 8);
        graphics.generateTexture('skill-coin', 24, 24);

        // Project crystal texture - more detailed crystal
        graphics.clear();
        graphics.fillStyle(0xff00ff);
        graphics.fillRect(16, 0, 16, 16); // top
        graphics.fillRect(8, 16, 32, 16); // middle
        graphics.fillRect(16, 32, 16, 16); // bottom
        graphics.fillStyle(0xff80ff);
        graphics.fillRect(18, 2, 12, 12); // top highlight
        graphics.fillRect(12, 18, 24, 12); // middle highlight
        graphics.fillRect(18, 34, 12, 12); // bottom highlight
        graphics.fillStyle(0xffffff);
        graphics.fillRect(20, 4, 2, 2); // sparkle
        graphics.fillRect(26, 8, 2, 2);
        graphics.fillRect(16, 20, 2, 2);
        graphics.fillRect(30, 24, 2, 2);
        graphics.generateTexture('project-crystal', 48, 48);

        // Platform texture - more detailed platform
        graphics.clear();
        graphics.fillStyle(0x8B4513);
        graphics.fillRect(0, 0, 32, 32);
        graphics.fillStyle(0x654321);
        graphics.fillRect(0, 0, 32, 4); // top edge
        graphics.fillRect(0, 28, 32, 4); // bottom edge
        graphics.fillStyle(0xa0522d);
        graphics.fillRect(2, 6, 28, 2); // wood grain
        graphics.fillRect(4, 12, 24, 2);
        graphics.fillRect(3, 18, 26, 2);
        graphics.fillRect(1, 24, 30, 2);
        graphics.generateTexture('platform', 32, 32);

        // Tree texture - more detailed tree
        graphics.clear();
        graphics.fillStyle(0x8B4513);
        graphics.fillRect(14, 20, 4, 12); // trunk
        graphics.fillStyle(0x228B22);
        graphics.fillCircle(16, 16, 14); // leaves
        graphics.fillStyle(0x32CD32);
        graphics.fillCircle(16, 16, 10); // inner leaves
        graphics.fillStyle(0x228B22);
        graphics.fillCircle(12, 12, 6); // left cluster
        graphics.fillCircle(20, 12, 6); // right cluster
        graphics.fillCircle(16, 8, 4); // top cluster
        graphics.generateTexture('tree', 32, 32);

        // Rock texture - more detailed rock
        graphics.clear();
        graphics.fillStyle(0x696969);
        graphics.fillRect(0, 0, 32, 32);
        graphics.fillStyle(0x808080);
        graphics.fillRect(2, 2, 28, 28);
        graphics.fillStyle(0x555555);
        graphics.fillRect(4, 4, 8, 8); // dark patches
        graphics.fillRect(16, 8, 6, 6);
        graphics.fillRect(8, 20, 10, 8);
        graphics.fillStyle(0x999999);
        graphics.fillRect(12, 6, 4, 4); // light patches
        graphics.fillRect(20, 16, 6, 4);
        graphics.fillRect(6, 24, 4, 4);
        graphics.generateTexture('rock', 32, 32);

        // Cloud texture - more detailed cloud
        graphics.clear();
        graphics.fillStyle(0xF0F8FF);
        graphics.fillCircle(8, 20, 8);
        graphics.fillCircle(16, 16, 10);
        graphics.fillCircle(24, 20, 8);
        graphics.fillCircle(12, 24, 6);
        graphics.fillCircle(20, 24, 6);
        graphics.fillStyle(0xFFFFFF);
        graphics.fillCircle(16, 18, 6); // highlight
        graphics.fillCircle(10, 22, 3);
        graphics.fillCircle(22, 22, 3);
        graphics.generateTexture('cloud', 32, 32);

        // Portal texture - more detailed portal
        graphics.clear();
        graphics.fillStyle(0x9400D3);
        graphics.fillCircle(32, 64, 30);
        graphics.fillStyle(0x4B0082);
        graphics.fillCircle(32, 64, 25);
        graphics.fillStyle(0x8A2BE2);
        graphics.fillCircle(32, 64, 20);
        graphics.fillStyle(0x9932CC);
        graphics.fillCircle(32, 64, 15);
        graphics.fillStyle(0x000000);
        graphics.fillCircle(32, 64, 10);
        // Add swirling effect
        graphics.fillStyle(0xFFFFFF);
        graphics.fillRect(30, 54, 2, 2);
        graphics.fillRect(38, 58, 2, 2);
        graphics.fillRect(34, 70, 2, 2);
        graphics.fillRect(26, 66, 2, 2);
        graphics.generateTexture('portal', 64, 128);

        // Clean up graphics object
        graphics.destroy();
    }
    
    create() {
        // Scene is ready, assets are loaded
        console.log('PreloadScene: Assets loaded successfully');
        
        // Initialize game state
        this.initializeGameState();
    }
    
    initializeGameState() {
        // Reset game state for new session
        GAME_CONFIG.gameState.skillCoinsCollected = 0;
        GAME_CONFIG.gameState.projectCrystalsCollected = 0;
        GAME_CONFIG.gameState.completedProjects = [];
        GAME_CONFIG.gameState.currentWorld = 'hub';
        
        // Update UI
        this.updateUI();
    }
    
    updateUI() {
        const skillCoinsElement = document.getElementById('skill-coins');
        const projectCrystalsElement = document.getElementById('project-crystals');
        
        if (skillCoinsElement) {
            skillCoinsElement.textContent = GAME_CONFIG.gameState.skillCoinsCollected;
        }
        
        if (projectCrystalsElement) {
            projectCrystalsElement.textContent = GAME_CONFIG.gameState.projectCrystalsCollected;
        }
    }
}
