// Portfolio data for The Developer's Quest
const PORTFOLIO_DATA = {
    developer: {
        name: "<PERSON> Developer",
        title: "Full Stack Developer & Game Enthusiast",
        bio: "Passionate developer who loves creating interactive experiences and solving complex problems through code.",
        contact: {
            email: "<EMAIL>",
            github: "https://github.com/alexdev",
            linkedin: "https://linkedin.com/in/alexdev",
            portfolio: "https://alexdev.com"
        }
    },
    
    worlds: {
        frontend: {
            name: "Frontend Forest",
            theme: "A vibrant, lush forest with interactive elements",
            projects: [
                {
                    id: "react-dashboard",
                    title: "Interactive Analytics Dashboard",
                    description: "A comprehensive dashboard built with React and D3.js featuring real-time data visualization, interactive charts, and responsive design. Includes user authentication and role-based access control.",
                    technologies: ["React", "TypeScript", "D3.js", "Tailwind CSS", "Redux"],
                    liveUrl: "https://dashboard-demo.com",
                    codeUrl: "https://github.com/alexdev/react-dashboard",
                    position: { x: 300, y: 400 }
                },
                {
                    id: "vue-ecommerce",
                    title: "E-commerce Platform",
                    description: "Modern e-commerce solution with Vue.js frontend, featuring product catalog, shopping cart, payment integration, and admin panel. Optimized for mobile and desktop.",
                    technologies: ["Vue.js", "Vuex", "SCSS", "Stripe API", "PWA"],
                    liveUrl: "https://shop-demo.com",
                    codeUrl: "https://github.com/alexdev/vue-shop",
                    position: { x: 800, y: 350 }
                },
                {
                    id: "portfolio-game",
                    title: "Gamified Portfolio",
                    description: "This very portfolio! A 2D platformer game built with Phaser 3 that showcases projects and skills through interactive gameplay. Features multiple worlds, collectibles, and responsive design.",
                    technologies: ["Phaser 3", "JavaScript", "HTML5 Canvas", "CSS3", "Web Audio API"],
                    liveUrl: "#",
                    codeUrl: "https://github.com/alexdev/portfolio-game",
                    position: { x: 1200, y: 300 }
                }
            ],
            skills: [
                { name: "React", icon: "⚛️", position: { x: 200, y: 450 } },
                { name: "Vue.js", icon: "💚", position: { x: 400, y: 380 } },
                { name: "TypeScript", icon: "🔷", position: { x: 600, y: 420 } },
                { name: "Tailwind CSS", icon: "🎨", position: { x: 500, y: 320 } },
                { name: "D3.js", icon: "📊", position: { x: 700, y: 380 } },
                { name: "SCSS", icon: "💅", position: { x: 900, y: 400 } },
                { name: "PWA", icon: "📱", position: { x: 1000, y: 350 } },
                { name: "Responsive Design", icon: "📐", position: { x: 1100, y: 420 } }
            ]
        },
        
        backend: {
            name: "Backend Caverns",
            theme: "Dark caves illuminated by glowing data streams",
            projects: [
                {
                    id: "node-api",
                    title: "RESTful API Service",
                    description: "Scalable Node.js API with Express, featuring JWT authentication, rate limiting, data validation, and comprehensive testing. Includes API documentation and monitoring.",
                    technologies: ["Node.js", "Express", "MongoDB", "JWT", "Jest"],
                    liveUrl: "https://api-demo.com/docs",
                    codeUrl: "https://github.com/alexdev/node-api",
                    position: { x: 400, y: 400 }
                },
                {
                    id: "python-ml",
                    title: "Machine Learning Pipeline",
                    description: "Data processing and ML pipeline using Python, featuring automated data cleaning, model training, and prediction API. Includes Docker containerization and CI/CD.",
                    technologies: ["Python", "FastAPI", "Pandas", "Scikit-learn", "Docker"],
                    liveUrl: "https://ml-demo.com",
                    codeUrl: "https://github.com/alexdev/ml-pipeline",
                    position: { x: 900, y: 350 }
                },
                {
                    id: "microservices",
                    title: "Microservices Architecture",
                    description: "Distributed system with multiple microservices, API gateway, service discovery, and event-driven communication. Deployed on Kubernetes with monitoring and logging.",
                    technologies: ["Go", "Docker", "Kubernetes", "Redis", "PostgreSQL"],
                    liveUrl: "https://microservices-demo.com",
                    codeUrl: "https://github.com/alexdev/microservices",
                    position: { x: 1300, y: 380 }
                }
            ],
            skills: [
                { name: "Node.js", icon: "🟢", position: { x: 250, y: 450 } },
                { name: "Python", icon: "🐍", position: { x: 350, y: 320 } },
                { name: "Go", icon: "🔵", position: { x: 550, y: 400 } },
                { name: "Express", icon: "🚀", position: { x: 650, y: 350 } },
                { name: "FastAPI", icon: "⚡", position: { x: 750, y: 420 } },
                { name: "MongoDB", icon: "🍃", position: { x: 850, y: 300 } },
                { name: "PostgreSQL", icon: "🐘", position: { x: 1050, y: 400 } },
                { name: "Redis", icon: "🔴", position: { x: 1150, y: 350 } },
                { name: "Docker", icon: "🐳", position: { x: 1250, y: 420 } }
            ]
        },
        
        cloud: {
            name: "Cloud Citadel",
            theme: "Futuristic floating city in the clouds",
            projects: [
                {
                    id: "aws-serverless",
                    title: "Serverless Application",
                    description: "Serverless web application using AWS Lambda, API Gateway, and DynamoDB. Features automatic scaling, cost optimization, and infrastructure as code with Terraform.",
                    technologies: ["AWS Lambda", "API Gateway", "DynamoDB", "Terraform", "CloudFormation"],
                    liveUrl: "https://serverless-demo.com",
                    codeUrl: "https://github.com/alexdev/aws-serverless",
                    position: { x: 500, y: 350 }
                },
                {
                    id: "k8s-deployment",
                    title: "Kubernetes Orchestration",
                    description: "Container orchestration platform with Kubernetes, featuring auto-scaling, rolling deployments, service mesh, and comprehensive monitoring with Prometheus and Grafana.",
                    technologies: ["Kubernetes", "Helm", "Prometheus", "Grafana", "Istio"],
                    liveUrl: "https://k8s-demo.com",
                    codeUrl: "https://github.com/alexdev/k8s-platform",
                    position: { x: 1000, y: 300 }
                }
            ],
            skills: [
                { name: "AWS", icon: "☁️", position: { x: 300, y: 400 } },
                { name: "Kubernetes", icon: "⚙️", position: { x: 400, y: 350 } },
                { name: "Docker", icon: "🐳", position: { x: 600, y: 380 } },
                { name: "Terraform", icon: "🏗️", position: { x: 700, y: 320 } },
                { name: "Prometheus", icon: "📈", position: { x: 800, y: 400 } },
                { name: "Grafana", icon: "📊", position: { x: 900, y: 350 } },
                { name: "CI/CD", icon: "🔄", position: { x: 1100, y: 380 } }
            ]
        }
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PORTFOLIO_DATA;
}
