// Collectibles system for The Developer's Quest
class CollectibleManager {
    constructor(scene) {
        this.scene = scene;
        this.skillCoins = null;
        this.projectCrystals = null;
        this.particleEffects = new ParticleEffectsManager(scene);
    }
    
    create() {
        // Create physics groups for collectibles
        this.skillCoins = this.scene.physics.add.group();
        this.projectCrystals = this.scene.physics.add.group();
        
        // Setup collision with player
        if (this.scene.player) {
            this.scene.physics.add.overlap(
                this.scene.player.sprite, 
                this.skillCoins, 
                this.collectSkillCoin, 
                null, 
                this.scene
            );
            
            this.scene.physics.add.overlap(
                this.scene.player.sprite, 
                this.projectCrystals, 
                this.collectProjectCrystal, 
                null, 
                this.scene
            );
        }
    }
    
    createSkillCoin(x, y, skillData) {
        const coin = this.skillCoins.create(x, y, 'skill-coin');
        coin.setScale(0.8);
        coin.body.setSize(20, 20);
        coin.skillData = skillData;
        
        // Add floating animation
        this.scene.tweens.add({
            targets: coin,
            y: y - 15,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        // Add rotation animation
        this.scene.tweens.add({
            targets: coin,
            rotation: Math.PI * 2,
            duration: 3000,
            repeat: -1,
            ease: 'Linear'
        });
        
        // Add glow effect
        const glow = this.scene.add.circle(x, y, 15, 0xffff00, 0.3);
        coin.glow = glow;
        
        this.scene.tweens.add({
            targets: glow,
            scaleX: 1.5,
            scaleY: 1.5,
            alpha: 0.1,
            duration: 1500,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        return coin;
    }
    
    createProjectCrystal(x, y, projectData) {
        const crystal = this.projectCrystals.create(x, y, 'project-crystal');
        crystal.setScale(1);
        crystal.body.setSize(40, 40);
        crystal.projectData = projectData;
        
        // Add floating animation
        this.scene.tweens.add({
            targets: crystal,
            y: y - 20,
            duration: 3000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        // Add pulsing glow effect
        const glow = this.scene.add.circle(x, y, 30, 0xff00ff, 0.4);
        crystal.glow = glow;
        
        this.scene.tweens.add({
            targets: glow,
            scaleX: 2,
            scaleY: 2,
            alpha: 0.1,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        // Add sparkle particles
        this.createSparkleEffect(crystal);
        
        return crystal;
    }
    
    createSparkleEffect(crystal) {
        const sparkles = [];
        
        for (let i = 0; i < 5; i++) {
            const sparkle = this.scene.add.circle(
                crystal.x + Phaser.Math.Between(-20, 20),
                crystal.y + Phaser.Math.Between(-20, 20),
                2,
                0xffffff,
                0.8
            );
            
            sparkles.push(sparkle);
            
            this.scene.tweens.add({
                targets: sparkle,
                alpha: 0,
                scaleX: 0,
                scaleY: 0,
                duration: 1000,
                delay: i * 200,
                repeat: -1,
                repeatDelay: 2000,
                ease: 'Power2'
            });
        }
        
        crystal.sparkles = sparkles;
    }
    
    collectSkillCoin(player, coin) {
        // Play enhanced collection effect
        this.particleEffects.createCollectionBurst(coin.x, coin.y, 0xffff00);
        this.particleEffects.createFloatingText(coin.x, coin.y - 30, '+1 Skill', '#ffff00');

        // Update game state
        GAME_CONFIG.gameState.skillCoinsCollected++;

        // Update UI
        this.updateUI();

        // Play sound effect (placeholder)
        // this.scene.sound.play('coin-collect');

        // Log skill collected
        if (coin.skillData) {
            console.log(`Collected skill: ${coin.skillData.name}`);
            this.particleEffects.createFloatingText(coin.x, coin.y - 50, coin.skillData.name, '#ffffff', '12px');
        }

        // Destroy coin and its effects
        if (coin.glow) {
            coin.glow.destroy();
        }
        coin.destroy();
    }
    
    collectProjectCrystal(player, crystal) {
        // Play enhanced collection effect
        this.particleEffects.createCollectionBurst(crystal.x, crystal.y, 0xff00ff);
        this.particleEffects.createFloatingText(crystal.x, crystal.y - 30, 'Project Unlocked!', '#ff00ff');
        this.particleEffects.createScreenShake(8, 300);

        // Update game state
        GAME_CONFIG.gameState.projectCrystalsCollected++;

        if (crystal.projectData) {
            GAME_CONFIG.gameState.completedProjects.push(crystal.projectData.id);

            // Show project modal
            this.showProjectModal(crystal.projectData);

            // Add project name floating text
            this.particleEffects.createFloatingText(crystal.x, crystal.y - 50, crystal.projectData.title, '#ffffff', '14px');
        }

        // Update UI
        this.updateUI();

        // Play sound effect (placeholder)
        // this.scene.sound.play('crystal-collect');

        // Destroy crystal and its effects
        if (crystal.glow) {
            crystal.glow.destroy();
        }
        if (crystal.sparkles) {
            crystal.sparkles.forEach(sparkle => sparkle.destroy());
        }
        crystal.destroy();
    }
    
    createCollectionEffect(x, y, color) {
        // Create burst effect
        const particles = [];
        
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const distance = 50;
            const targetX = x + Math.cos(angle) * distance;
            const targetY = y + Math.sin(angle) * distance;
            
            const particle = this.scene.add.circle(x, y, 4, color, 0.8);
            particles.push(particle);
            
            this.scene.tweens.add({
                targets: particle,
                x: targetX,
                y: targetY,
                alpha: 0,
                scaleX: 0,
                scaleY: 0,
                duration: 500,
                ease: 'Power2',
                onComplete: () => {
                    particle.destroy();
                }
            });
        }
        
        // Create expanding ring
        const ring = this.scene.add.circle(x, y, 10, color, 0.5);
        ring.setStrokeStyle(3, color);
        ring.setFillStyle();
        
        this.scene.tweens.add({
            targets: ring,
            scaleX: 3,
            scaleY: 3,
            alpha: 0,
            duration: 600,
            ease: 'Power2',
            onComplete: () => {
                ring.destroy();
            }
        });
    }
    
    showProjectModal(projectData) {
        const modal = document.getElementById('project-modal');
        const title = document.getElementById('project-title');
        const description = document.getElementById('project-description');
        const techTags = document.getElementById('project-tech');
        const viewLiveBtn = document.getElementById('view-live-btn');
        const viewCodeBtn = document.getElementById('view-code-btn');
        
        if (modal && title && description && techTags) {
            // Populate modal content
            title.textContent = projectData.title;
            description.textContent = projectData.description;
            
            // Create technology tags
            techTags.innerHTML = '';
            projectData.technologies.forEach(tech => {
                const tag = document.createElement('div');
                tag.className = 'tech-tag';
                tag.textContent = tech;
                techTags.appendChild(tag);
            });
            
            // Setup buttons
            viewLiveBtn.style.display = 'inline-block';
            viewLiveBtn.textContent = 'View Live';
            viewLiveBtn.onclick = () => {
                if (projectData.liveUrl && projectData.liveUrl !== '#') {
                    window.open(projectData.liveUrl, '_blank');
                }
            };
            
            viewCodeBtn.style.display = 'inline-block';
            viewCodeBtn.textContent = 'See Code';
            viewCodeBtn.onclick = () => {
                if (projectData.codeUrl) {
                    window.open(projectData.codeUrl, '_blank');
                }
            };
            
            // Show modal
            modal.style.display = 'block';
            
            // Pause game
            this.scene.scene.pause();
        }
    }
    
    updateUI() {
        const skillCoinsElement = document.getElementById('skill-coins');
        const projectCrystalsElement = document.getElementById('project-crystals');
        
        if (skillCoinsElement) {
            skillCoinsElement.textContent = GAME_CONFIG.gameState.skillCoinsCollected;
        }
        
        if (projectCrystalsElement) {
            projectCrystalsElement.textContent = GAME_CONFIG.gameState.projectCrystalsCollected;
        }
    }
    
    loadWorldCollectibles(worldKey) {
        const worldData = PORTFOLIO_DATA.worlds[worldKey];
        if (!worldData) return;
        
        // Create skill coins
        worldData.skills.forEach(skill => {
            this.createSkillCoin(skill.position.x, skill.position.y, skill);
        });
        
        // Create project crystals
        worldData.projects.forEach(project => {
            this.createProjectCrystal(project.position.x, project.position.y, project);
        });
    }
    
    destroy() {
        if (this.skillCoins) {
            this.skillCoins.clear(true, true);
        }
        if (this.projectCrystals) {
            this.projectCrystals.clear(true, true);
        }
    }
}

// Global function to close project modal
function closeProjectModal() {
    const modal = document.getElementById('project-modal');
    if (modal) {
        modal.style.display = 'none';
        
        // Resume game if it was paused
        const game = window.game;
        if (game && game.scene.isActive('HubScene')) {
            game.scene.resume('HubScene');
        } else if (game && game.scene.isActive('FrontendScene')) {
            game.scene.resume('FrontendScene');
        } else if (game && game.scene.isActive('BackendScene')) {
            game.scene.resume('BackendScene');
        } else if (game && game.scene.isActive('CloudScene')) {
            game.scene.resume('CloudScene');
        }
    }
}
