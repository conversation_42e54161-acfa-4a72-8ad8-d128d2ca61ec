# Requirements & Plan: The Developer's Quest - A Gamified Portfolio

## 1. High-Level Vision & Core Concept

**Project Goal:** To create a fully interactive, 2D platformer game that serves as a portfolio. The user plays the game to explore the developer's projects, skills, and background.

**Core Metaphor:** The game is "The Developer's Quest." The player character, a stylized avatar of the developer, embarks on a quest through different "worlds." Each world represents a domain of expertise (e.g., Frontend, Backend), and the collectibles within represent completed projects and acquired skills.

**User Experience:** The primary experience is gameplay. The user should feel like they are playing a fun, retro-inspired indie game. Portfolio information is the reward for exploration and achievement, not a passive document to be read.

## 2. Game Mechanics & Gameplay

*   **Genre:** 2D Side-Scrolling Platformer.
*   **Player Controls:**
    *   **Keyboard:** Left Arrow (move left), Right Arrow (move right), Spacebar (jump).
    *   **On-Screen (for Mobile):** D-pad and a jump button.
*   **Core Gameplay Loop:**
    1.  The player starts in a central "Hub World."
    2.  The player enters a portal to a "World" (e.g., "The Frontend Forest").
    3.  The player navigates the level by running and jumping, avoiding simple obstacles.
    4.  The player collects "Project Crystals" and "Skill Coins."
    5.  Collecting a "Project Crystal" reveals details about a specific project.
    6.  The player reaches the end of the level to unlock the next stage or return to the Hub.
*   **Collectibles:**
    *   **Project Crystals (Major Collectible):** Large, glowing crystals. Each one represents a major portfolio project. There should be 3-5 per world.
    *   **Skill Coins (Minor Collectible):** Small, spinning coins with icons representing technologies (e.g., React logo, Node.js logo, Python logo). These are scattered liberally throughout the levels.
*   **Obstacles & Enemies:**
    *   Keep it simple and non-violent. The focus is exploration, not difficult combat.
    *   **Obstacles:** Moving platforms, disappearing blocks, water hazards, spike pits (non-lethal, just respawns the player at a checkpoint).
    *   **"Enemies":** Small, cute "bugs" that patrol a small area. Jumping on them makes them disappear in a puff of smoke. Contact with them resets the player to a checkpoint.

## 3. World & Level Design

*   **Hub World:** A small, central starting area. It contains:
    *   The player character.
    *   Portals to each of the main "Worlds."
    *   An "About Me" NPC (Non-Player Character) the player can talk to.
    *   A "Contact" statue or shrine, which is initially inactive.
*   **World 1: The Frontend Forest**
    *   **Theme:** A vibrant, lush forest with a day/night cycle.
    *   **Elements:** Bouncy mushrooms, climbable vines, tree-top platforms.
*   **World 2: The Backend Caverns**
    *   **Theme:** A dark, mysterious cave system illuminated by glowing crystals and streams of "data-lava."
    *   **Elements:** Crumbling platforms, steam vents that push the player, minecart-style moving platforms.
*   **World 3: The Cloud Citadel**
    *   **Theme:** A futuristic city floating in the sky.
    *   **Elements:** Anti-gravity zones, gusts of wind that affect jumping, teleportation pads.
*   **Final Goal:** After collecting all "Project Crystals" from all worlds, the "Contact" shrine in the Hub World activates, providing a call to action and contact links.

## 4. UI/UX - Information Display

*   **Game HUD (Heads-Up Display):**
    *   Minimalist and styled to match the game's aesthetic (e.g., pixel art).
    *   Top-Left: A counter for collected "Skill Coins."
    *   Top-Right: A counter for collected "Project Crystals" in the current level.
*   **Project Detail View:**
    *   **Trigger:** Collecting a "Project Crystal."
    *   **Display:** A pop-up modal that feels like part of the game (e.g., an ancient scroll unrolling, a holographic display). The game world should pause and blur in the background.
    *   **Content:** Project Title, a short and engaging description, a list of "Skill Coins" (technologies) used, a "View Live" button, and a "See Code" button.
*   **Pause Menu:**
    *   **Trigger:** Pressing the "ESC" key.
    *   **Options:** Resume, Restart Level, Return to Hub.
*   **Skills Inventory Screen:**
    *   **Access:** From the Pause Menu or by talking to a specific NPC in the Hub.
    *   **Display:** A grid showing all the "Skill Coins" the player has collected, acting as a dynamic skills list.

## 5. Art Style & Audio

*   **Visuals:** 16-bit pixel art. This style is nostalgic, performant on the web, and allows for a lot of character.
*   **Color Palette:** Each world should have a distinct and vibrant color palette.
*   **Animations:** Fluid and "juicy" animations for player movement (idle, run, jump, land), item collection, and UI elements.
*   **Audio:**
    *   **Music:** A chiptune soundtrack. Each world should have its own distinct background track. The Hub World should have a calm, welcoming theme.
    *   **Sound Effects:** Retro sound effects for jumping, collecting items, interacting with the UI, and other player actions.

## 6. Technical Plan & Stack

*   **Game Engine:** **Phaser 3** (or a similar 2D web game library like Kaboom.js). Phaser is recommended for its maturity, feature set, and large community.
*   **Rendering:** WebGL with a Canvas fallback.
*   **UI Framework:** The UI modals (Project Details, etc.) can be built using the game engine's native UI elements or by overlaying HTML/CSS elements managed by a lightweight library like **Preact** or **Svelte** for better control and styling.
*   **Asset Management:**
    *   Use spritesheets for animations to reduce texture swaps.
    *   Use a tilemap editor like **Tiled** to design levels.
*   **Data Structure:** All portfolio data (projects, skills, descriptions) should be stored in a single, well-structured `portfolio-data.json` file. The game will fetch this file to populate the levels and UI dynamically.

## 7. AI Development Plan - Step-by-Step

1.  **Phase 1: Core Game Setup**
    *   Initialize a new project with Phaser 3.
    *   Create a blank game scene.
    *   Load a placeholder player sprite and implement basic movement: left, right, and jump with physics (gravity).
    *   Set up the camera to follow the player.

2.  **Phase 2: Level Prototyping**
    *   Create a simple level using a Tiled map and load it into Phaser.
    *   Implement basic tile-based collision (making platforms solid).

3.  **Phase 3: Collectibles & Data**
    *   Create the `portfolio-data.json` file.
    *   Implement the "Project Crystal" and "Skill Coin" as interactive sprites.
    *   Write the logic to dynamically place these collectibles in the level based on the JSON data.

4.  **Phase 4: UI & Information Modals**
    *   Build the in-game HUD to track collected items.
    *   Create the HTML/CSS structure for the Project Detail modal.
    *   Write the logic to show/hide the modal when a "Project Crystal" is collected and populate it with the correct data from the JSON file.

5.  **Phase 5: World Expansion**
    *   Design and build the three main worlds ("Frontend Forest," "Backend Caverns," "Cloud Citadel") and the Hub World using Tiled.
    *   Implement the specific mechanics for each world (e.g., bouncy mushrooms, moving platforms).

6.  **Phase 6: Polish, Audio & Art**
    *   Replace all placeholder art with final pixel art assets (player, tilesets, items).
    *   Integrate the chiptune music and sound effects.
    *   Add "juice": particle effects on item collection, screen shake on impact, coyote time for jumps, etc.

7.  **Phase 7: Finalization & Deployment**
    *   Implement the final "Contact" shrine logic.
    *   Conduct thorough testing and bug fixing.
    *   Optimize assets for the web (compress images and audio).
    *   Deploy the project as a static website to a service like Netlify, Vercel, or GitHub Pages.
