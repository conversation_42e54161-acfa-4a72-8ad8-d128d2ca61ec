// Backend Caverns Scene for The Developer's Quest
class BackendScene extends Phaser.Scene {
    constructor() {
        super({ key: 'BackendScene' });
        this.player = null;
        this.platforms = null;
        this.collectibleManager = null;
        this.movingPlatforms = null;
        this.steamVents = null;
        this.exitPortal = null;
        this.backgroundElements = null;
    }
    
    create() {
        console.log('BackendScene: Entering Backend Caverns');
        
        // Set world bounds
        this.physics.world.setBounds(0, 0, GAME_CONFIG.level.worldWidth, GAME_CONFIG.level.worldHeight);
        
        // Create background
        this.createBackground();
        
        // Create platforms and world geometry
        this.createPlatforms();
        
        // Create player
        this.createPlayer();
        
        // Create collectibles
        this.createCollectibles();
        
        // Create interactive elements
        this.createInteractiveElements();
        
        // Create exit portal
        this.createExitPortal();
        
        // Setup camera
        this.setupCamera();
        
        // Setup input
        this.setupInput();
        
        // Update game state
        GAME_CONFIG.gameState.currentWorld = 'backend';
    }
    
    createBackground() {
        // Create dark cave background with gradient
        const graphics = this.add.graphics();
        graphics.fillGradientStyle(0x3d2a1a, 0x3d2a1a, 0x1a1a1a, 0x1a1a1a, 1);
        graphics.fillRect(0, 0, GAME_CONFIG.level.worldWidth, GAME_CONFIG.level.worldHeight);
        
        // Add background cave elements
        this.backgroundElements = this.add.group();
        
        // Glowing crystals for illumination
        for (let i = 0; i < 15; i++) {
            const x = Phaser.Math.Between(100, GAME_CONFIG.level.worldWidth - 100);
            const y = Phaser.Math.Between(100, 400);
            
            const crystal = this.add.circle(x, y, 8, 0x00ffff, 0.6);
            this.backgroundElements.add(crystal);
            
            // Add glow effect
            const glow = this.add.circle(x, y, 20, 0x00ffff, 0.2);
            this.backgroundElements.add(glow);
            
            this.tweens.add({
                targets: [crystal, glow],
                alpha: 0.3,
                duration: Phaser.Math.Between(2000, 4000),
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        }
        
        // Data streams (flowing lines)
        for (let i = 0; i < 8; i++) {
            const x = Phaser.Math.Between(0, GAME_CONFIG.level.worldWidth);
            const startY = Phaser.Math.Between(0, 200);
            const endY = startY + 300;
            
            const stream = this.add.line(0, 0, x, startY, x, endY, 0x00ff00, 0.4);
            stream.setLineWidth(2);
            this.backgroundElements.add(stream);
            
            // Animate data flow
            this.tweens.add({
                targets: stream,
                alpha: 0.1,
                duration: 1500,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        }
    }
    
    createPlatforms() {
        this.platforms = this.physics.add.staticGroup();
        
        // Ground platforms
        for (let x = 0; x < GAME_CONFIG.level.worldWidth; x += 32) {
            const platform = this.platforms.create(x + 16, GAME_CONFIG.level.worldHeight - 16, 'rock');
            platform.setTint(0x696969);
            platform.setScale(1).refreshBody();
        }
        
        // Cave platforms with crumbling effect
        const platformPositions = [
            { x: 200, y: 450 },
            { x: 400, y: 400 },
            { x: 600, y: 350 },
            { x: 800, y: 300 },
            { x: 1000, y: 350 },
            { x: 1200, y: 300 },
            { x: 1400, y: 250 }
        ];
        
        platformPositions.forEach(pos => {
            const platform = this.platforms.create(pos.x, pos.y, 'rock');
            platform.setTint(0x8B4513);
            platform.setScale(1.2, 0.8).refreshBody();
        });
        
        // Stalactites and stalagmites
        for (let i = 0; i < 10; i++) {
            const x = Phaser.Math.Between(100, GAME_CONFIG.level.worldWidth - 100);
            
            // Stalactite (hanging from ceiling)
            const stalactite = this.platforms.create(x, 50, 'rock');
            stalactite.setTint(0x2F4F4F);
            stalactite.setScale(0.5, 2).refreshBody();
            
            // Stalagmite (rising from ground)
            if (Phaser.Math.Between(0, 1)) {
                const stalagmite = this.platforms.create(x + 50, GAME_CONFIG.level.worldHeight - 80, 'rock');
                stalagmite.setTint(0x2F4F4F);
                stalagmite.setScale(0.5, 1.5).refreshBody();
            }
        }
    }
    
    createPlayer() {
        const startX = 100;
        const startY = 400;
        
        this.player = new Player(this, startX, startY);
        
        // Setup collision with platforms
        this.physics.add.collider(this.player.sprite, this.platforms);
    }
    
    createCollectibles() {
        this.collectibleManager = new CollectibleManager(this);
        this.collectibleManager.create();
        
        // Load Backend world collectibles
        this.collectibleManager.loadWorldCollectibles('backend');
    }
    
    createInteractiveElements() {
        // Create steam vents that push the player
        this.steamVents = this.physics.add.group();
        
        const ventPositions = [
            { x: 300, y: GAME_CONFIG.level.worldHeight - 50 },
            { x: 700, y: GAME_CONFIG.level.worldHeight - 50 },
            { x: 1100, y: GAME_CONFIG.level.worldHeight - 50 }
        ];
        
        ventPositions.forEach(pos => {
            const vent = this.steamVents.create(pos.x, pos.y, 'platform');
            vent.setTint(0xFF4500);
            vent.setScale(0.5, 0.3).refreshBody();
            vent.body.setImmovable(true);
            
            // Create steam effect
            const steam = this.add.circle(pos.x, pos.y - 30, 15, 0xFFFFFF, 0.3);
            
            this.tweens.add({
                targets: steam,
                scaleX: 2,
                scaleY: 3,
                alpha: 0,
                y: pos.y - 100,
                duration: 1000,
                repeat: -1,
                ease: 'Power2'
            });
        });
        
        // Setup steam vent collision
        this.physics.add.overlap(this.player.sprite, this.steamVents, this.pushPlayer, null, this);
        
        // Create minecart-style moving platforms
        this.movingPlatforms = this.physics.add.group();
        
        const cartData = [
            { x: 500, y: 380, endX: 650, duration: 3000 },
            { x: 900, y: 280, endX: 1050, duration: 2500 },
            { x: 1300, y: 230, endX: 1450, duration: 3500 }
        ];
        
        cartData.forEach(data => {
            const cart = this.movingPlatforms.create(data.x, data.y, 'platform');
            cart.setTint(0x8B4513);
            cart.body.setImmovable(true);
            cart.body.setSize(48, 16);
            
            this.tweens.add({
                targets: cart,
                x: data.endX,
                duration: data.duration,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        });
        
        this.physics.add.collider(this.player.sprite, this.movingPlatforms);
    }
    
    createExitPortal() {
        // Portal back to hub
        this.exitPortal = this.physics.add.group();
        const portal = this.exitPortal.create(GAME_CONFIG.level.worldWidth - 100, 400, 'portal');
        portal.setScale(0.6);
        portal.destination = 'HubScene';
        
        // Add portal animation
        this.tweens.add({
            targets: portal,
            scaleX: 0.7,
            scaleY: 0.7,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        this.tweens.add({
            targets: portal,
            rotation: Math.PI * 2,
            duration: 6000,
            repeat: -1,
            ease: 'Linear'
        });
        
        // Setup portal collision
        this.physics.add.overlap(this.player.sprite, this.exitPortal, this.exitWorld, null, this);
    }
    
    setupCamera() {
        // Camera follows player
        this.cameras.main.startFollow(this.player.sprite);
        this.cameras.main.setBounds(0, 0, GAME_CONFIG.level.worldWidth, GAME_CONFIG.level.worldHeight);
        
        // Set camera zoom for cave atmosphere
        this.cameras.main.setZoom(1.1);
    }
    
    setupInput() {
        // Pause key
        this.input.keyboard.on('keydown-ESC', () => {
            this.scene.pause();
        });
        
        // Return to hub key
        this.input.keyboard.on('keydown-H', () => {
            this.scene.start('HubScene');
        });
    }
    
    pushPlayer(player, vent) {
        // Push player upward with steam
        player.body.setVelocityY(-400);
        
        // Create steam burst effect
        const effect = this.add.circle(vent.x, vent.y - 20, 25, 0xFFFFFF, 0.6);
        
        this.tweens.add({
            targets: effect,
            scaleX: 3,
            scaleY: 3,
            alpha: 0,
            duration: 500,
            ease: 'Power2',
            onComplete: () => {
                effect.destroy();
            }
        });
    }
    
    exitWorld(player, portal) {
        // Add exit effect
        const effect = this.add.circle(portal.x, portal.y, 80, 0x9400D3, 0.5);
        effect.setScale(0);
        
        this.tweens.add({
            targets: effect,
            scaleX: 2,
            scaleY: 2,
            alpha: 0,
            duration: 500,
            ease: 'Power2',
            onComplete: () => {
                effect.destroy();
                this.scene.start(portal.destination);
            }
        });
        
        // Disable player movement
        player.body.setVelocity(0, 0);
        
        console.log('Returning to Hub World');
    }
    
    update(time, delta) {
        if (this.player) {
            this.player.update(delta);
        }
        
        // Update moving platform physics
        if (this.movingPlatforms) {
            this.movingPlatforms.children.entries.forEach(platform => {
                platform.body.velocity.x = 0;
                platform.body.velocity.y = 0;
            });
        }
    }
    
    shutdown() {
        // Clean up when leaving scene
        if (this.collectibleManager) {
            this.collectibleManager.destroy();
        }
    }
}
