<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terminal Quest - Interactive Portfolio</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Fira Code', monospace;
            background: #0a0a0a;
            color: #00ff00;
            overflow: hidden;
            height: 100vh;
        }
        
        .terminal {
            width: 100%;
            height: 100vh;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            padding: 20px;
            display: flex;
            flex-direction: column;
            position: relative;
        }
        
        .terminal::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 255, 0, 0.03) 2px,
                    rgba(0, 255, 0, 0.03) 4px
                );
            pointer-events: none;
            z-index: 1;
        }
        
        .header {
            border: 1px solid #00ff00;
            padding: 10px;
            margin-bottom: 20px;
            background: rgba(0, 255, 0, 0.05);
            position: relative;
            z-index: 2;
        }
        
        .header h1 {
            color: #00ffff;
            font-size: 1.5rem;
            margin-bottom: 5px;
        }
        
        .header p {
            color: #888;
            font-size: 0.9rem;
        }
        
        .game-area {
            flex: 1;
            display: flex;
            gap: 20px;
            position: relative;
            z-index: 2;
        }
        
        .map-container {
            flex: 1;
            border: 1px solid #00ff00;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            position: relative;
        }
        
        .map {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            grid-template-rows: repeat(8, 1fr);
            gap: 2px;
            height: 100%;
            max-height: 400px;
        }
        
        .cell {
            background: #111;
            border: 1px solid #333;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .cell:hover {
            background: rgba(0, 255, 0, 0.1);
            border-color: #00ff00;
        }
        
        .cell.player {
            background: #00ffff;
            color: #000;
            animation: pulse 2s infinite;
        }
        
        .cell.project {
            background: rgba(255, 0, 255, 0.3);
            color: #ff00ff;
            animation: glow 3s infinite;
        }
        
        .cell.skill {
            background: rgba(255, 255, 0, 0.3);
            color: #ffff00;
        }
        
        .cell.wall {
            background: #444;
            color: #666;
        }
        
        .cell.visited {
            background: rgba(0, 255, 0, 0.1);
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px rgba(255, 0, 255, 0.5); }
            50% { box-shadow: 0 0 20px rgba(255, 0, 255, 0.8); }
        }
        
        .info-panel {
            width: 350px;
            border: 1px solid #00ff00;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            overflow-y: auto;
        }
        
        .stats {
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #333;
            background: rgba(0, 255, 0, 0.05);
        }
        
        .stats h3 {
            color: #00ffff;
            margin-bottom: 10px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        
        .commands {
            margin-bottom: 20px;
        }
        
        .commands h3 {
            color: #00ffff;
            margin-bottom: 10px;
        }
        
        .command {
            margin: 5px 0;
            padding: 5px;
            background: rgba(0, 255, 0, 0.1);
            border-left: 3px solid #00ff00;
            font-size: 0.9rem;
        }
        
        .log {
            flex: 1;
            border: 1px solid #333;
            padding: 10px;
            background: rgba(0, 0, 0, 0.5);
            overflow-y: auto;
            max-height: 200px;
        }
        
        .log h3 {
            color: #00ffff;
            margin-bottom: 10px;
        }
        
        .log-entry {
            margin: 3px 0;
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .log-entry.success {
            color: #00ff00;
        }
        
        .log-entry.info {
            color: #00ffff;
        }
        
        .log-entry.warning {
            color: #ffff00;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-content {
            background: #0a0a0a;
            border: 2px solid #00ff00;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }
        
        .modal-content h2 {
            color: #00ffff;
            margin-bottom: 15px;
        }
        
        .modal-content p {
            margin: 10px 0;
            line-height: 1.6;
        }
        
        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 15px 0;
        }
        
        .tech-tag {
            background: rgba(0, 255, 255, 0.2);
            border: 1px solid #00ffff;
            padding: 4px 8px;
            font-size: 0.8rem;
            border-radius: 3px;
        }
        
        .modal-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .btn {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: rgba(0, 255, 0, 0.4);
            transform: translateY(-2px);
        }
        
        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: #ff0000;
            font-size: 1.5rem;
            cursor: pointer;
            font-family: 'Fira Code', monospace;
        }
        
        .progress-bar {
            width: 100%;
            height: 10px;
            background: #333;
            border: 1px solid #00ff00;
            margin: 5px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff00, #00ffff);
            transition: width 0.5s ease;
        }
        
        @media (max-width: 768px) {
            .game-area {
                flex-direction: column;
            }
            
            .info-panel {
                width: 100%;
                height: 200px;
            }
            
            .map {
                max-height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="terminal">
        <div class="header">
            <h1>TERMINAL QUEST v2.0</h1>
            <p>Navigate the digital realm to discover projects and skills</p>
        </div>
        
        <div class="game-area">
            <div class="map-container">
                <div class="map" id="gameMap"></div>
            </div>
            
            <div class="info-panel">
                <div class="stats">
                    <h3>PLAYER STATS</h3>
                    <div class="stat-item">
                        <span>Position:</span>
                        <span id="playerPos">0,0</span>
                    </div>
                    <div class="stat-item">
                        <span>Projects Found:</span>
                        <span id="projectsFound">0/6</span>
                    </div>
                    <div class="stat-item">
                        <span>Skills Collected:</span>
                        <span id="skillsFound">0/12</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressBar" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="commands">
                    <h3>COMMANDS</h3>
                    <div class="command">WASD / Arrow Keys - Move</div>
                    <div class="command">SPACE - Interact</div>
                    <div class="command">ESC - Close Modal</div>
                    <div class="command">R - Reset Game</div>
                </div>
                
                <div class="log">
                    <h3>SYSTEM LOG</h3>
                    <div id="gameLog">
                        <div class="log-entry info">System initialized...</div>
                        <div class="log-entry success">Player spawned at (0,0)</div>
                        <div class="log-entry info">Use WASD to explore the digital realm</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="modal" id="gameModal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeModal()">&times;</button>
            <h2 id="modalTitle">Project Title</h2>
            <p id="modalDescription">Project description goes here...</p>
            <div class="tech-tags" id="modalTech"></div>
            <div class="modal-buttons">
                <a href="#" class="btn" id="liveBtn" target="_blank">View Live</a>
                <a href="#" class="btn" id="codeBtn" target="_blank">View Code</a>
            </div>
        </div>
    </div>
    
    <script src="terminal-game.js"></script>
</body>
</html>
