# The Developer's Quest - A Gamified Portfolio

A 2D platformer game that serves as an interactive portfolio, built with Phaser 3. Explore different worlds representing various domains of expertise, collect skill coins, and discover project crystals to learn about the developer's work.

## 🎮 Game Features

### Core Gameplay
- **2D Side-Scrolling Platformer** with smooth physics and responsive controls
- **Three Unique Worlds** each representing different development domains:
  - 🌲 **Frontend Forest** - Web development and UI/UX projects
  - 🏔️ **Backend Caverns** - Server-side and database projects  
  - ☁️ **Cloud Citadel** - DevOps and cloud infrastructure projects
- **Hub World** - Central area connecting all worlds with portals

### Interactive Elements
- **Skill Coins** - Collectible items representing technologies and skills
- **Project Crystals** - Major collectibles that reveal detailed project information
- **Interactive NPCs** - About Me character and Contact shrine
- **Special Mechanics** per world:
  - Frontend: Bouncy mushrooms, moving vine platforms
  - Backend: Steam vents, minecart-style moving platforms
  - Cloud: Wind zones, teleportation pads

### Visual Effects
- **Particle Systems** - Explosions, sparkles, trails, and screen shake
- **Pixel Art Style** - 16-bit inspired graphics with modern polish
- **Smooth Animations** - Floating collectibles, portal effects, environmental animations
- **Responsive UI** - Clean HUD and modal system for project details

## 🎯 Controls

### Desktop
- **Arrow Keys** or **WASD** - Move left/right
- **Spacebar** or **Up Arrow** - Jump
- **ESC** - Pause game
- **H** - Return to Hub (from any world)

### Mobile
- **On-screen D-pad** - Touch controls for movement and jumping
- **Responsive Design** - Adapts to different screen sizes

## 🛠️ Technical Stack

- **Game Engine**: Phaser 3 (WebGL/Canvas)
- **Languages**: JavaScript (ES6+), HTML5, CSS3
- **Architecture**: Modular scene-based system
- **Features**:
  - Physics-based movement with coyote time and jump buffering
  - Dynamic collectible placement from JSON data
  - Particle effects system
  - Mobile-responsive controls
  - Modal-based project showcase

## 📁 Project Structure

```
├── index.html              # Main HTML file
├── server.js              # Development server
├── js/
│   ├── main.js            # Game initialization
│   ├── game-config.js     # Configuration settings
│   ├── portfolio-data.js  # Project and skill data
│   ├── player.js          # Player character logic
│   ├── collectibles.js    # Collectible system
│   ├── particle-effects.js # Visual effects
│   ├── ui-manager.js      # UI and modal management
│   └── scenes/
│       ├── preload-scene.js   # Asset loading
│       ├── hub-scene.js       # Central hub world
│       ├── frontend-scene.js  # Frontend world
│       ├── backend-scene.js   # Backend world
│       └── cloud-scene.js     # Cloud world
└── README.md
```

## 🚀 Getting Started

### Prerequisites
- Node.js (for development server)
- Modern web browser with WebGL support

### Installation & Running

1. **Clone or download** the project files
2. **Start the development server**:
   ```bash
   node server.js
   ```
3. **Open your browser** and navigate to:
   ```
   http://localhost:8000
   ```

### Alternative Servers
You can also use any static file server:
```bash
# Python
python -m http.server 8000

# Node.js (if you have http-server installed)
npx http-server -p 8000 -c-1
```

## 🎨 Customization

### Adding Your Own Projects
Edit `js/portfolio-data.js` to customize:
- **Developer information** (name, bio, contact details)
- **Project details** (title, description, technologies, links)
- **Skill sets** (technologies and tools)
- **Collectible positions** (x, y coordinates in each world)

### Styling
- **Colors and themes** can be modified in `index.html` CSS
- **Game configuration** (physics, player speed, etc.) in `js/game-config.js`
- **Visual effects** can be customized in `js/particle-effects.js`

## 🎯 Game Objectives

1. **Explore all three worlds** by entering portals from the Hub
2. **Collect Skill Coins** to build your technology portfolio
3. **Find Project Crystals** to unlock detailed project information
4. **Complete all projects** to activate the Contact shrine
5. **Connect with the developer** through the activated Contact portal

## 🌟 Features Highlights

- **Responsive Design** - Works on desktop and mobile devices
- **Smooth Physics** - Includes advanced platformer mechanics like coyote time
- **Visual Polish** - Particle effects, screen shake, and smooth animations
- **Interactive Portfolio** - Projects revealed through gameplay rather than static pages
- **Modular Architecture** - Easy to extend with new worlds and features

## 📱 Browser Compatibility

- **Chrome/Chromium** - Full support
- **Firefox** - Full support  
- **Safari** - Full support
- **Edge** - Full support
- **Mobile browsers** - Touch controls enabled

## 🔧 Development Notes

The game uses a modular architecture with separate scene files for each world. The collectible system dynamically places items based on the portfolio data, making it easy to update content without modifying game code.

Key technical features:
- **Scene management** with Phaser 3's scene system
- **Physics-based movement** with arcade physics
- **Event-driven architecture** for UI interactions
- **Responsive scaling** for different screen sizes
- **Particle system** for enhanced visual feedback

---

**Ready to embark on The Developer's Quest?** 🚀

Start the server and begin your journey through the worlds of web development!
