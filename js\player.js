// Player class for The Developer's Quest
class Player {
    constructor(scene, x, y) {
        this.scene = scene;
        this.sprite = null;
        this.cursors = null;
        this.wasdKeys = null;
        this.isGrounded = false;
        this.canJump = true;
        this.coyoteTime = 0;
        this.jumpBufferTime = 0;
        this.particleEffects = new ParticleEffectsManager(scene);
        this.lastGroundedTime = 0;

        this.create(x, y);
        this.setupInput();
        this.setupAnimations();
    }
    
    create(x, y) {
        // Create player sprite (using a colored rectangle for now)
        this.sprite = this.scene.add.rectangle(x, y, 
            GAME_CONFIG.player.size.width, 
            GAME_CONFIG.player.size.height, 
            0x00ffff
        );
        
        // Enable physics
        this.scene.physics.add.existing(this.sprite);
        this.sprite.body.setCollideWorldBounds(true);
        this.sprite.body.setSize(
            GAME_CONFIG.player.size.width - 4, 
            GAME_CONFIG.player.size.height - 4
        );
        
        // Set bounce and drag
        this.sprite.body.setBounce(0.1);
        this.sprite.body.setDragX(800);
        
        // Store reference to player in sprite for collision detection
        this.sprite.player = this;
    }
    
    setupInput() {
        // Keyboard input
        this.cursors = this.scene.input.keyboard.createCursorKeys();
        this.wasdKeys = this.scene.input.keyboard.addKeys('W,S,A,D,SPACE');
        
        // Mobile input setup
        this.setupMobileControls();
    }
    
    setupMobileControls() {
        // Mobile control state
        this.mobileInput = {
            left: false,
            right: false,
            jump: false
        };
        
        // Get mobile control elements
        const leftBtn = document.getElementById('left-btn');
        const rightBtn = document.getElementById('right-btn');
        const jumpBtn = document.getElementById('jump-btn');
        
        if (leftBtn && rightBtn && jumpBtn) {
            // Left button
            leftBtn.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.mobileInput.left = true;
            });
            leftBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                this.mobileInput.left = false;
            });
            leftBtn.addEventListener('mousedown', (e) => {
                e.preventDefault();
                this.mobileInput.left = true;
            });
            leftBtn.addEventListener('mouseup', (e) => {
                e.preventDefault();
                this.mobileInput.left = false;
            });
            
            // Right button
            rightBtn.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.mobileInput.right = true;
            });
            rightBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                this.mobileInput.right = false;
            });
            rightBtn.addEventListener('mousedown', (e) => {
                e.preventDefault();
                this.mobileInput.right = true;
            });
            rightBtn.addEventListener('mouseup', (e) => {
                e.preventDefault();
                this.mobileInput.right = false;
            });
            
            // Jump button
            jumpBtn.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.mobileInput.jump = true;
            });
            jumpBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                this.mobileInput.jump = false;
            });
            jumpBtn.addEventListener('mousedown', (e) => {
                e.preventDefault();
                this.mobileInput.jump = true;
            });
            jumpBtn.addEventListener('mouseup', (e) => {
                e.preventDefault();
                this.mobileInput.jump = false;
            });
        }
    }
    
    setupAnimations() {
        // Placeholder for animations - will be implemented when we have sprites
        this.currentAnimation = 'idle';
    }
    
    update(delta) {
        this.handleMovement();
        this.handleJumping(delta);
        this.updateGroundedState();
        this.updateAnimation();
        this.updateParticleEffects();
    }

    updateParticleEffects() {
        // Running dust trail
        if (this.isGrounded && Math.abs(this.sprite.body.velocity.x) > 100) {
            if (Math.random() < 0.3) { // 30% chance each frame
                this.particleEffects.createTrail(
                    this.sprite.x + Phaser.Math.Between(-10, 10),
                    this.sprite.y + 20,
                    0xcccccc,
                    2
                );
            }
        }
    }
    
    handleMovement() {
        const leftPressed = this.cursors.left.isDown || 
                           this.wasdKeys.A.isDown || 
                           this.mobileInput.left;
        const rightPressed = this.cursors.right.isDown || 
                            this.wasdKeys.D.isDown || 
                            this.mobileInput.right;
        
        if (leftPressed) {
            this.sprite.body.setVelocityX(-GAME_CONFIG.player.speed);
            this.sprite.setFlipX(true);
        } else if (rightPressed) {
            this.sprite.body.setVelocityX(GAME_CONFIG.player.speed);
            this.sprite.setFlipX(false);
        } else {
            this.sprite.body.setVelocityX(0);
        }
    }
    
    handleJumping(delta) {
        const jumpPressed = this.cursors.up.isDown || 
                           this.cursors.space.isDown || 
                           this.wasdKeys.W.isDown || 
                           this.wasdKeys.SPACE.isDown ||
                           this.mobileInput.jump;
        
        // Coyote time - allow jumping for a short time after leaving ground
        if (this.isGrounded) {
            this.coyoteTime = 150; // milliseconds
            this.canJump = true;
        } else {
            this.coyoteTime -= delta;
        }
        
        // Jump buffer - register jump input slightly before landing
        if (jumpPressed) {
            this.jumpBufferTime = 100; // milliseconds
        } else {
            this.jumpBufferTime -= delta;
        }
        
        // Execute jump
        if (this.jumpBufferTime > 0 && this.coyoteTime > 0 && this.canJump) {
            this.sprite.body.setVelocityY(GAME_CONFIG.player.jumpVelocity);
            this.canJump = false;
            this.jumpBufferTime = 0;
            this.coyoteTime = 0;

            // Add jump effects
            this.particleEffects.createDustCloud(this.sprite.x, this.sprite.y + 20, 0xcccccc);
            this.particleEffects.createScreenShake(3, 100);

            // Play jump sound effect (placeholder)
            // this.scene.sound.play('jump');
        }
    }
    
    updateGroundedState() {
        const wasGrounded = this.isGrounded;
        this.isGrounded = this.sprite.body.touching.down || this.sprite.body.blocked.down;

        // Landing effect
        if (!wasGrounded && this.isGrounded && this.sprite.body.velocity.y > 200) {
            this.particleEffects.createDustCloud(this.sprite.x, this.sprite.y + 20, 0xcccccc);
            this.particleEffects.createScreenShake(2, 80);
            this.lastGroundedTime = this.scene.time.now;
        }
    }
    
    updateAnimation() {
        let newAnimation = 'idle';
        
        if (!this.isGrounded) {
            newAnimation = this.sprite.body.velocity.y < 0 ? 'jump' : 'fall';
        } else if (Math.abs(this.sprite.body.velocity.x) > 10) {
            newAnimation = 'run';
        }
        
        if (newAnimation !== this.currentAnimation) {
            this.currentAnimation = newAnimation;
            // Play animation when we have sprite sheets
            // this.sprite.play(newAnimation);
        }
    }
    
    getPosition() {
        return {
            x: this.sprite.x,
            y: this.sprite.y
        };
    }
    
    setPosition(x, y) {
        this.sprite.setPosition(x, y);
    }
    
    respawn(x, y) {
        this.setPosition(x, y);
        this.sprite.body.setVelocity(0, 0);

        // Add enhanced respawn effect
        this.particleEffects.createPortalEffect(x, y, 40);
        this.particleEffects.createRipple(x, y, 0x00ffff, 3);
        this.particleEffects.createSparkles(x, y, 15, 0x00ffff);
        this.particleEffects.createScreenShake(5, 200);
    }
    
    destroy() {
        if (this.sprite) {
            this.sprite.destroy();
        }
    }
}
