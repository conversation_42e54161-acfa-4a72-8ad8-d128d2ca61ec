// Hub Scene for The Developer's Quest
class HubScene extends Phaser.Scene {
    constructor() {
        super({ key: 'HubScene' });
        this.player = null;
        this.platforms = null;
        this.portals = null;
        this.npcs = null;
    }
    
    create() {
        console.log('HubScene: Starting Hub World');
        
        // Set world bounds
        this.physics.world.setBounds(0, 0, GAME_CONFIG.level.worldWidth, GAME_CONFIG.level.worldHeight);
        
        // Create background
        this.createBackground();
        
        // Create platforms and world geometry
        this.createPlatforms();
        
        // Create player
        this.createPlayer();
        
        // Create portals to other worlds
        this.createPortals();
        
        // Create NPCs
        this.createNPCs();
        
        // Setup camera
        this.setupCamera();
        
        // Setup input
        this.setupInput();
        
        // Update game state
        GAME_CONFIG.gameState.currentWorld = 'hub';
    }
    
    createBackground() {
        // Create a gradient background
        const graphics = this.add.graphics();
        graphics.fillGradientStyle(0x1a1a2e, 0x1a1a2e, 0x2d4a87, 0x2d4a87, 1);
        graphics.fillRect(0, 0, GAME_CONFIG.level.worldWidth, GAME_CONFIG.level.worldHeight);
        
        // Add some decorative elements
        for (let i = 0; i < 20; i++) {
            const x = Phaser.Math.Between(0, GAME_CONFIG.level.worldWidth);
            const y = Phaser.Math.Between(0, GAME_CONFIG.level.worldHeight - 100);
            const star = this.add.circle(x, y, 2, 0xffffff, 0.8);
            
            // Add twinkling effect
            this.tweens.add({
                targets: star,
                alpha: 0.3,
                duration: Phaser.Math.Between(1000, 3000),
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        }
    }
    
    createPlatforms() {
        this.platforms = this.physics.add.staticGroup();
        
        // Ground platform
        for (let x = 0; x < GAME_CONFIG.level.worldWidth; x += 32) {
            const platform = this.platforms.create(x + 16, GAME_CONFIG.level.worldHeight - 16, 'platform');
            platform.setScale(1).refreshBody();
        }
        
        // Central hub platforms
        const centerX = GAME_CONFIG.level.worldWidth / 2;
        
        // Main central platform
        for (let i = -3; i <= 3; i++) {
            this.platforms.create(centerX + (i * 32), 450, 'platform');
        }
        
        // Portal platforms
        this.platforms.create(200, 400, 'platform');
        this.platforms.create(centerX - 300, 350, 'platform');
        this.platforms.create(centerX + 300, 350, 'platform');
        this.platforms.create(centerX, 250, 'platform');
        
        // Decorative platforms
        for (let i = 0; i < 10; i++) {
            const x = Phaser.Math.Between(100, GAME_CONFIG.level.worldWidth - 100);
            const y = Phaser.Math.Between(200, 400);
            this.platforms.create(x, y, 'platform');
        }
    }
    
    createPlayer() {
        const startX = GAME_CONFIG.player.startPosition.x;
        const startY = GAME_CONFIG.player.startPosition.y;
        
        this.player = new Player(this, startX, startY);
        
        // Setup collision with platforms
        this.physics.add.collider(this.player.sprite, this.platforms);
    }
    
    createPortals() {
        this.portals = this.physics.add.group();
        
        const centerX = GAME_CONFIG.level.worldWidth / 2;
        
        // Frontend Forest Portal
        const frontendPortal = this.portals.create(centerX - 300, 270, 'portal');
        frontendPortal.setScale(0.8);
        frontendPortal.destination = 'FrontendScene';
        frontendPortal.worldName = 'Frontend Forest';
        
        // Backend Caverns Portal
        const backendPortal = this.portals.create(centerX + 300, 270, 'portal');
        backendPortal.setScale(0.8);
        backendPortal.destination = 'BackendScene';
        backendPortal.worldName = 'Backend Caverns';
        
        // Cloud Citadel Portal
        const cloudPortal = this.portals.create(centerX, 170, 'portal');
        cloudPortal.setScale(0.8);
        cloudPortal.destination = 'CloudScene';
        cloudPortal.worldName = 'Cloud Citadel';
        
        // Add portal animations
        this.portals.children.entries.forEach(portal => {
            this.tweens.add({
                targets: portal,
                scaleX: portal.scaleX * 1.1,
                scaleY: portal.scaleY * 1.1,
                duration: 2000,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
            
            this.tweens.add({
                targets: portal,
                rotation: Math.PI * 2,
                duration: 8000,
                repeat: -1,
                ease: 'Linear'
            });
        });
        
        // Setup portal collision
        this.physics.add.overlap(this.player.sprite, this.portals, this.enterPortal, null, this);
    }
    
    createNPCs() {
        this.npcs = this.physics.add.group();
        
        // About Me NPC
        const aboutNPC = this.npcs.create(200, 350, 'player');
        aboutNPC.setTint(0x90EE90);
        aboutNPC.npcType = 'about';
        aboutNPC.body.setImmovable(true);
        
        // Contact Shrine (initially inactive)
        const contactShrine = this.npcs.create(GAME_CONFIG.level.worldWidth / 2, 400, 'project-crystal');
        contactShrine.setTint(0x808080); // Gray when inactive
        contactShrine.npcType = 'contact';
        contactShrine.active = false;
        contactShrine.body.setImmovable(true);
        
        // Add floating animation to NPCs
        this.npcs.children.entries.forEach(npc => {
            this.tweens.add({
                targets: npc,
                y: npc.y - 10,
                duration: 2000,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        });
        
        // Setup NPC interaction
        this.physics.add.overlap(this.player.sprite, this.npcs, this.interactWithNPC, null, this);
    }
    
    setupCamera() {
        // Camera follows player
        this.cameras.main.startFollow(this.player.sprite);
        this.cameras.main.setBounds(0, 0, GAME_CONFIG.level.worldWidth, GAME_CONFIG.level.worldHeight);
        
        // Set camera zoom
        this.cameras.main.setZoom(1);
    }
    
    setupInput() {
        // Pause key
        this.input.keyboard.on('keydown-ESC', () => {
            this.scene.pause();
            // Show pause menu (to be implemented)
        });
    }
    
    enterPortal(player, portal) {
        // Add portal entry effect
        const effect = this.add.circle(portal.x, portal.y, 100, 0x9400D3, 0.5);
        effect.setScale(0);
        
        this.tweens.add({
            targets: effect,
            scaleX: 2,
            scaleY: 2,
            alpha: 0,
            duration: 500,
            ease: 'Power2',
            onComplete: () => {
                effect.destroy();
                this.scene.start(portal.destination);
            }
        });
        
        // Disable player movement temporarily
        player.body.setVelocity(0, 0);
        
        console.log(`Entering ${portal.worldName}`);
    }
    
    interactWithNPC(player, npc) {
        if (npc.npcType === 'about') {
            this.showAboutDialog();
        } else if (npc.npcType === 'contact' && npc.active) {
            this.showContactDialog();
        }
    }
    
    showAboutDialog() {
        // Show about information in the project modal
        const modal = document.getElementById('project-modal');
        const title = document.getElementById('project-title');
        const description = document.getElementById('project-description');
        const techTags = document.getElementById('project-tech');
        const viewLiveBtn = document.getElementById('view-live-btn');
        const viewCodeBtn = document.getElementById('view-code-btn');
        
        if (modal && title && description) {
            title.textContent = PORTFOLIO_DATA.developer.name;
            description.textContent = PORTFOLIO_DATA.developer.bio;
            
            // Clear tech tags and add role
            techTags.innerHTML = `<div class="tech-tag">${PORTFOLIO_DATA.developer.title}</div>`;
            
            // Hide action buttons for about dialog
            viewLiveBtn.style.display = 'none';
            viewCodeBtn.style.display = 'none';
            
            modal.style.display = 'block';
        }
    }
    
    showContactDialog() {
        // Show contact information
        const modal = document.getElementById('project-modal');
        const title = document.getElementById('project-title');
        const description = document.getElementById('project-description');
        const techTags = document.getElementById('project-tech');
        const viewLiveBtn = document.getElementById('view-live-btn');
        const viewCodeBtn = document.getElementById('view-code-btn');
        
        if (modal && title && description) {
            title.textContent = 'Get In Touch!';
            description.textContent = 'Thanks for playing through my portfolio! Ready to work together?';
            
            // Add contact links as tech tags
            techTags.innerHTML = `
                <div class="tech-tag">📧 ${PORTFOLIO_DATA.developer.contact.email}</div>
                <div class="tech-tag">🐙 GitHub</div>
                <div class="tech-tag">💼 LinkedIn</div>
            `;
            
            // Show contact buttons
            viewLiveBtn.textContent = 'LinkedIn';
            viewLiveBtn.style.display = 'inline-block';
            viewLiveBtn.onclick = () => window.open(PORTFOLIO_DATA.developer.contact.linkedin, '_blank');
            
            viewCodeBtn.textContent = 'GitHub';
            viewCodeBtn.style.display = 'inline-block';
            viewCodeBtn.onclick = () => window.open(PORTFOLIO_DATA.developer.contact.github, '_blank');
            
            modal.style.display = 'block';
        }
    }
    
    update(time, delta) {
        if (this.player) {
            this.player.update(delta);
        }
        
        // Check if contact shrine should be activated
        this.checkContactShrineActivation();
    }
    
    checkContactShrineActivation() {
        // Activate contact shrine when all project crystals are collected
        const totalCrystals = Object.values(PORTFOLIO_DATA.worlds).reduce((total, world) => {
            return total + world.projects.length;
        }, 0);
        
        if (GAME_CONFIG.gameState.projectCrystalsCollected >= totalCrystals) {
            const contactShrine = this.npcs.children.entries.find(npc => npc.npcType === 'contact');
            if (contactShrine && !contactShrine.active) {
                contactShrine.active = true;
                contactShrine.setTint(0x00ffff); // Activate with cyan color
                
                // Add activation effect
                const effect = this.add.circle(contactShrine.x, contactShrine.y, 50, 0x00ffff, 0.3);
                this.tweens.add({
                    targets: effect,
                    scaleX: 3,
                    scaleY: 3,
                    alpha: 0,
                    duration: 1000,
                    ease: 'Power2'
                });
            }
        }
    }
}
