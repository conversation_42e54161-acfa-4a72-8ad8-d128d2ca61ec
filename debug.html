<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - The Developer's Quest</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: 'Courier New', monospace;
        }
        #debug-info {
            margin-bottom: 20px;
            padding: 10px;
            background: #2a2a4e;
            border-radius: 5px;
        }
        #game-container {
            border: 2px solid #16213e;
            border-radius: 8px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div id="debug-info">
        <h2>Debug Information</h2>
        <div id="status">Loading...</div>
        <div id="errors"></div>
    </div>
    
    <div id="game-container"></div>

    <!-- Phaser 3 CDN -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <script>
        const statusDiv = document.getElementById('status');
        const errorsDiv = document.getElementById('errors');
        
        function updateStatus(message) {
            statusDiv.innerHTML += '<br>' + message;
            console.log(message);
        }
        
        function logError(error) {
            errorsDiv.innerHTML += '<br><span style="color: red;">ERROR: ' + error + '</span>';
            console.error(error);
        }
        
        // Catch all errors
        window.addEventListener('error', (e) => {
            logError(e.message + ' at ' + e.filename + ':' + e.lineno);
        });
        
        updateStatus('1. DOM loaded');
        
        // Check if Phaser loaded
        if (typeof Phaser !== 'undefined') {
            updateStatus('2. Phaser 3 loaded successfully (version: ' + Phaser.VERSION + ')');
        } else {
            logError('Phaser 3 failed to load');
        }
        
        // Simple Phaser test
        try {
            updateStatus('3. Creating simple Phaser game...');
            
            const config = {
                type: Phaser.AUTO,
                width: 800,
                height: 600,
                parent: 'game-container',
                backgroundColor: '#2d4a87',
                scene: {
                    preload: function() {
                        updateStatus('4. Preload function called');
                        // Create a simple colored rectangle
                        this.add.graphics()
                            .fillStyle(0x00ffff)
                            .fillRect(0, 0, 32, 32)
                            .generateTexture('player', 32, 32);
                    },
                    create: function() {
                        updateStatus('5. Create function called');
                        
                        // Add a simple player sprite
                        const player = this.add.image(400, 300, 'player');
                        
                        // Add some text
                        this.add.text(400, 100, 'Simple Phaser Test', {
                            fontSize: '32px',
                            color: '#ffffff'
                        }).setOrigin(0.5);
                        
                        this.add.text(400, 150, 'If you see this, Phaser is working!', {
                            fontSize: '16px',
                            color: '#00ffff'
                        }).setOrigin(0.5);
                        
                        updateStatus('6. Game created successfully!');
                    }
                }
            };
            
            const game = new Phaser.Game(config);
            updateStatus('7. Phaser.Game instance created');
            
        } catch (error) {
            logError('Failed to create Phaser game: ' + error.message);
        }
    </script>
</body>
</html>
