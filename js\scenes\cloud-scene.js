// Cloud Citadel Scene for The Developer's Quest
class CloudScene extends Phaser.Scene {
    constructor() {
        super({ key: 'CloudScene' });
        this.player = null;
        this.platforms = null;
        this.collectibleManager = null;
        this.windZones = null;
        this.teleportPads = null;
        this.exitPortal = null;
        this.backgroundElements = null;
    }
    
    create() {
        console.log('CloudScene: Entering Cloud Citadel');
        
        // Set world bounds
        this.physics.world.setBounds(0, 0, GAME_CONFIG.level.worldWidth, GAME_CONFIG.level.worldHeight);
        
        // Create background
        this.createBackground();
        
        // Create platforms and world geometry
        this.createPlatforms();
        
        // Create player
        this.createPlayer();
        
        // Create collectibles
        this.createCollectibles();
        
        // Create interactive elements
        this.createInteractiveElements();
        
        // Create exit portal
        this.createExitPortal();
        
        // Setup camera
        this.setupCamera();
        
        // Setup input
        this.setupInput();
        
        // Update game state
        GAME_CONFIG.gameState.currentWorld = 'cloud';
    }
    
    createBackground() {
        // Create sky background with gradient
        const graphics = this.add.graphics();
        graphics.fillGradientStyle(0x4a4a6a, 0x4a4a6a, 0x87CEEB, 0x87CEEB, 1);
        graphics.fillRect(0, 0, GAME_CONFIG.level.worldWidth, GAME_CONFIG.level.worldHeight);
        
        // Add background sky elements
        this.backgroundElements = this.add.group();
        
        // Floating clouds
        for (let i = 0; i < 20; i++) {
            const x = Phaser.Math.Between(0, GAME_CONFIG.level.worldWidth);
            const y = Phaser.Math.Between(0, 300);
            
            const cloud = this.add.image(x, y, 'cloud');
            cloud.setScale(Phaser.Math.FloatBetween(0.5, 1.2));
            cloud.setTint(0xF0F8FF);
            cloud.setAlpha(0.6);
            this.backgroundElements.add(cloud);
            
            // Floating animation
            this.tweens.add({
                targets: cloud,
                x: x + Phaser.Math.Between(-50, 50),
                y: y + Phaser.Math.Between(-20, 20),
                duration: Phaser.Math.Between(8000, 15000),
                repeat: -1,
                yoyo: true,
                ease: 'Sine.easeInOut'
            });
        }
        
        // Futuristic city elements in the background
        for (let i = 0; i < 8; i++) {
            const x = Phaser.Math.Between(100, GAME_CONFIG.level.worldWidth - 100);
            const y = Phaser.Math.Between(300, 500);
            const height = Phaser.Math.Between(100, 200);
            
            const building = this.add.rectangle(x, y, 20, height, 0x4169E1, 0.4);
            this.backgroundElements.add(building);
            
            // Add glowing windows
            for (let j = 0; j < 5; j++) {
                const windowY = y - height/2 + (j * 20) + 10;
                const window = this.add.circle(x, windowY, 2, 0x00ffff, 0.8);
                this.backgroundElements.add(window);
                
                this.tweens.add({
                    targets: window,
                    alpha: 0.3,
                    duration: Phaser.Math.Between(1000, 3000),
                    yoyo: true,
                    repeat: -1,
                    ease: 'Sine.easeInOut'
                });
            }
        }
        
        // Energy streams connecting buildings
        for (let i = 0; i < 5; i++) {
            const startX = Phaser.Math.Between(100, GAME_CONFIG.level.worldWidth - 200);
            const endX = startX + Phaser.Math.Between(100, 200);
            const y = Phaser.Math.Between(350, 450);
            
            const stream = this.add.line(0, 0, startX, y, endX, y, 0x00ffff, 0.3);
            stream.setLineWidth(3);
            this.backgroundElements.add(stream);
            
            this.tweens.add({
                targets: stream,
                alpha: 0.1,
                duration: 2000,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        }
    }
    
    createPlatforms() {
        this.platforms = this.physics.add.staticGroup();
        
        // Floating cloud platforms
        const platformPositions = [
            { x: 150, y: 500 },
            { x: 350, y: 450 },
            { x: 550, y: 400 },
            { x: 750, y: 350 },
            { x: 950, y: 300 },
            { x: 1150, y: 250 },
            { x: 1350, y: 300 },
            { x: 1500, y: 200 }
        ];
        
        platformPositions.forEach(pos => {
            const platform = this.platforms.create(pos.x, pos.y, 'cloud');
            platform.setTint(0xF0F8FF);
            platform.setScale(1.5, 0.8).refreshBody();
            
            // Add floating animation
            this.tweens.add({
                targets: platform,
                y: pos.y - 10,
                duration: 3000,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        });
        
        // Futuristic metal platforms
        const metalPlatforms = [
            { x: 250, y: 380 },
            { x: 650, y: 280 },
            { x: 1050, y: 180 },
            { x: 1250, y: 220 }
        ];
        
        metalPlatforms.forEach(pos => {
            const platform = this.platforms.create(pos.x, pos.y, 'platform');
            platform.setTint(0x4169E1);
            platform.setScale(1.2, 0.6).refreshBody();
        });
    }
    
    createPlayer() {
        const startX = 100;
        const startY = 450;
        
        this.player = new Player(this, startX, startY);
        
        // Setup collision with platforms
        this.physics.add.collider(this.player.sprite, this.platforms);
    }
    
    createCollectibles() {
        this.collectibleManager = new CollectibleManager(this);
        this.collectibleManager.create();
        
        // Load Cloud world collectibles
        this.collectibleManager.loadWorldCollectibles('cloud');
    }
    
    createInteractiveElements() {
        // Create wind zones that affect jumping
        this.windZones = this.physics.add.group();
        
        const windZoneData = [
            { x: 400, y: 300, width: 100, height: 200, direction: 'up' },
            { x: 800, y: 250, width: 80, height: 150, direction: 'right' },
            { x: 1200, y: 200, width: 120, height: 180, direction: 'left' }
        ];
        
        windZoneData.forEach(data => {
            const zone = this.windZones.create(data.x, data.y, null);
            zone.setSize(data.width, data.height);
            zone.setVisible(false);
            zone.windDirection = data.direction;
            zone.body.setImmovable(true);
            
            // Create visual wind effect
            for (let i = 0; i < 10; i++) {
                const particle = this.add.circle(
                    data.x + Phaser.Math.Between(-data.width/2, data.width/2),
                    data.y + Phaser.Math.Between(-data.height/2, data.height/2),
                    2,
                    0xFFFFFF,
                    0.5
                );
                
                let targetX = particle.x;
                let targetY = particle.y;
                
                switch(data.direction) {
                    case 'up':
                        targetY -= 100;
                        break;
                    case 'right':
                        targetX += 100;
                        break;
                    case 'left':
                        targetX -= 100;
                        break;
                }
                
                this.tweens.add({
                    targets: particle,
                    x: targetX,
                    y: targetY,
                    alpha: 0,
                    duration: 1500,
                    repeat: -1,
                    ease: 'Linear'
                });
            }
        });
        
        // Setup wind zone collision
        this.physics.add.overlap(this.player.sprite, this.windZones, this.applyWind, null, this);
        
        // Create teleportation pads
        this.teleportPads = this.physics.add.group();
        
        const teleportData = [
            { x: 300, y: 420, targetX: 700, targetY: 320 },
            { x: 900, y: 270, targetX: 1300, targetY: 270 }
        ];
        
        teleportData.forEach(data => {
            const pad = this.teleportPads.create(data.x, data.y, 'platform');
            pad.setTint(0x9400D3);
            pad.setScale(0.8).refreshBody();
            pad.targetX = data.targetX;
            pad.targetY = data.targetY;
            
            // Add teleport effect
            this.tweens.add({
                targets: pad,
                scaleX: 1,
                scaleY: 1,
                duration: 1000,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
            
            // Create energy rings
            const ring = this.add.circle(data.x, data.y - 20, 30, 0x9400D3, 0.3);
            ring.setStrokeStyle(3, 0x9400D3);
            ring.setFillStyle();
            
            this.tweens.add({
                targets: ring,
                scaleX: 2,
                scaleY: 2,
                alpha: 0,
                duration: 2000,
                repeat: -1,
                ease: 'Power2'
            });
        });
        
        // Setup teleport collision
        this.physics.add.overlap(this.player.sprite, this.teleportPads, this.teleportPlayer, null, this);
    }
    
    createExitPortal() {
        // Portal back to hub
        this.exitPortal = this.physics.add.group();
        const portal = this.exitPortal.create(GAME_CONFIG.level.worldWidth - 100, 150, 'portal');
        portal.setScale(0.6);
        portal.destination = 'HubScene';
        
        // Add portal animation
        this.tweens.add({
            targets: portal,
            scaleX: 0.7,
            scaleY: 0.7,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        this.tweens.add({
            targets: portal,
            rotation: Math.PI * 2,
            duration: 6000,
            repeat: -1,
            ease: 'Linear'
        });
        
        // Setup portal collision
        this.physics.add.overlap(this.player.sprite, this.exitPortal, this.exitWorld, null, this);
    }
    
    setupCamera() {
        // Camera follows player
        this.cameras.main.startFollow(this.player.sprite);
        this.cameras.main.setBounds(0, 0, GAME_CONFIG.level.worldWidth, GAME_CONFIG.level.worldHeight);
        
        // Set camera zoom for sky view
        this.cameras.main.setZoom(1.0);
    }
    
    setupInput() {
        // Pause key
        this.input.keyboard.on('keydown-ESC', () => {
            this.scene.pause();
        });
        
        // Return to hub key
        this.input.keyboard.on('keydown-H', () => {
            this.scene.start('HubScene');
        });
    }
    
    applyWind(player, windZone) {
        const windForce = 300;
        
        switch(windZone.windDirection) {
            case 'up':
                if (player.body.velocity.y > -200) {
                    player.body.setVelocityY(-windForce);
                }
                break;
            case 'right':
                player.body.setVelocityX(windForce);
                break;
            case 'left':
                player.body.setVelocityX(-windForce);
                break;
        }
    }
    
    teleportPlayer(player, pad) {
        // Create teleport effect at current position
        const startEffect = this.add.circle(player.x, player.y, 50, 0x9400D3, 0.6);
        
        this.tweens.add({
            targets: startEffect,
            scaleX: 0,
            scaleY: 0,
            duration: 300,
            ease: 'Power2',
            onComplete: () => {
                startEffect.destroy();
                
                // Teleport player
                player.setPosition(pad.targetX, pad.targetY - 50);
                
                // Create arrival effect
                const endEffect = this.add.circle(pad.targetX, pad.targetY - 50, 50, 0x9400D3, 0.6);
                
                this.tweens.add({
                    targets: endEffect,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 500,
                    ease: 'Power2',
                    onComplete: () => {
                        endEffect.destroy();
                    }
                });
            }
        });
    }
    
    exitWorld(player, portal) {
        // Add exit effect
        const effect = this.add.circle(portal.x, portal.y, 80, 0x9400D3, 0.5);
        effect.setScale(0);
        
        this.tweens.add({
            targets: effect,
            scaleX: 2,
            scaleY: 2,
            alpha: 0,
            duration: 500,
            ease: 'Power2',
            onComplete: () => {
                effect.destroy();
                this.scene.start(portal.destination);
            }
        });
        
        // Disable player movement
        player.body.setVelocity(0, 0);
        
        console.log('Returning to Hub World');
    }
    
    update(time, delta) {
        if (this.player) {
            this.player.update(delta);
        }
    }
    
    shutdown() {
        // Clean up when leaving scene
        if (this.collectibleManager) {
            this.collectibleManager.destroy();
        }
    }
}
