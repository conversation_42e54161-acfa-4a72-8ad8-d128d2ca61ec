// Space Explorer Portfolio Game
class SpaceExplorer {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.player = null;
        this.controls = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            boost: false
        };
        this.mouse = { x: 0, y: 0 };
        this.velocity = new THREE.Vector3();
        this.direction = new THREE.Vector3();
        
        this.projectsFound = 0;
        this.skillsFound = 0;
        this.totalProjects = 8;
        this.totalSkills = 15;
        this.explorationProgress = 0;
        
        this.interactables = [];
        this.particles = [];
        this.stars = [];
        
        this.portfolioData = {
            projects: [
                {
                    title: "AI-Powered E-commerce Platform",
                    description: "Revolutionary e-commerce platform with AI-driven product recommendations, real-time inventory management, and advanced analytics. Built with microservices architecture for scalability.",
                    technologies: ["React", "Node.js", "TensorFlow", "MongoDB", "Docker", "AWS"],
                    liveUrl: "https://ai-ecommerce-demo.com",
                    codeUrl: "https://github.com/user/ai-ecommerce",
                    position: { x: 50, y: 10, z: 30 },
                    color: 0xff6b6b
                },
                {
                    title: "Real-time Collaboration Suite",
                    description: "Complete collaboration platform with video conferencing, shared whiteboards, real-time document editing, and project management tools. Supports thousands of concurrent users.",
                    technologies: ["Vue.js", "Socket.io", "WebRTC", "Redis", "PostgreSQL"],
                    liveUrl: "https://collab-suite-demo.com",
                    codeUrl: "https://github.com/user/collab-suite",
                    position: { x: -40, y: 15, z: -25 },
                    color: 0x4ecdc4
                },
                {
                    title: "Blockchain Voting System",
                    description: "Secure, transparent voting system built on blockchain technology. Features voter verification, immutable vote records, and real-time result tracking with complete audit trails.",
                    technologies: ["Solidity", "Web3.js", "React", "IPFS", "Ethereum"],
                    liveUrl: "https://blockchain-voting-demo.com",
                    codeUrl: "https://github.com/user/blockchain-voting",
                    position: { x: 30, y: -20, z: 45 },
                    color: 0x45b7d1
                },
                {
                    title: "IoT Smart Home Dashboard",
                    description: "Comprehensive IoT dashboard for smart home management. Controls lighting, temperature, security systems, and energy consumption with predictive analytics and automation.",
                    technologies: ["Angular", "Python", "MQTT", "InfluxDB", "Grafana"],
                    liveUrl: "https://smart-home-demo.com",
                    codeUrl: "https://github.com/user/smart-home",
                    position: { x: -30, y: 25, z: 20 },
                    color: 0xffd93d
                },
                {
                    title: "Machine Learning Pipeline",
                    description: "End-to-end ML pipeline for predictive analytics with automated data preprocessing, model training, hyperparameter tuning, and deployment. Handles petabytes of data.",
                    technologies: ["Python", "TensorFlow", "Kubernetes", "Apache Spark", "MLflow"],
                    liveUrl: "https://ml-pipeline-demo.com",
                    codeUrl: "https://github.com/user/ml-pipeline",
                    position: { x: 60, y: -10, z: -40 },
                    color: 0xa29bfe
                },
                {
                    title: "Augmented Reality Shopping",
                    description: "AR shopping experience allowing customers to visualize products in their space before purchase. Includes 3D product modeling, spatial tracking, and social sharing features.",
                    technologies: ["Unity", "ARCore", "C#", "Firebase", "Blender"],
                    liveUrl: "https://ar-shopping-demo.com",
                    codeUrl: "https://github.com/user/ar-shopping",
                    position: { x: -50, y: 5, z: 35 },
                    color: 0xfd79a8
                },
                {
                    title: "Cybersecurity Monitoring Platform",
                    description: "Advanced cybersecurity platform with real-time threat detection, automated incident response, and comprehensive security analytics. Protects enterprise networks 24/7.",
                    technologies: ["Go", "Elasticsearch", "Kibana", "Docker", "Prometheus"],
                    liveUrl: "https://cybersec-demo.com",
                    codeUrl: "https://github.com/user/cybersec-platform",
                    position: { x: 20, y: 30, z: -30 },
                    color: 0xe17055
                },
                {
                    title: "Quantum Computing Simulator",
                    description: "Quantum computing simulator for educational and research purposes. Simulates quantum circuits, implements quantum algorithms, and provides visualization tools.",
                    technologies: ["Python", "Qiskit", "NumPy", "Matplotlib", "Jupyter"],
                    liveUrl: "https://quantum-sim-demo.com",
                    codeUrl: "https://github.com/user/quantum-simulator",
                    position: { x: -20, y: -25, z: -50 },
                    color: 0x00b894
                }
            ],
            skills: [
                { name: "JavaScript", position: { x: 15, y: 8, z: 12 }, color: 0xf1c40f },
                { name: "Python", position: { x: -25, y: 12, z: -15 }, color: 0x3498db },
                { name: "React", position: { x: 35, y: -5, z: 25 }, color: 0x61dafb },
                { name: "Node.js", position: { x: -15, y: 18, z: 8 }, color: 0x68a063 },
                { name: "Docker", position: { x: 45, y: 3, z: -20 }, color: 0x2496ed },
                { name: "AWS", position: { x: -35, y: -8, z: 30 }, color: 0xff9900 },
                { name: "MongoDB", position: { x: 25, y: 15, z: -35 }, color: 0x4db33d },
                { name: "PostgreSQL", position: { x: -45, y: 20, z: 15 }, color: 0x336791 },
                { name: "TensorFlow", position: { x: 55, y: -15, z: 5 }, color: 0xff6f00 },
                { name: "Kubernetes", position: { x: -20, y: -18, z: 40 }, color: 0x326ce5 },
                { name: "GraphQL", position: { x: 40, y: 25, z: -10 }, color: 0xe10098 },
                { name: "TypeScript", position: { x: -30, y: 8, z: -25 }, color: 0x3178c6 },
                { name: "Vue.js", position: { x: 10, y: -12, z: 35 }, color: 0x4fc08d },
                { name: "Redis", position: { x: -40, y: 15, z: -5 }, color: 0xdc382d },
                { name: "Blockchain", position: { x: 30, y: -20, z: -45 }, color: 0xf7931a }
            ]
        };
        
        this.init();
    }
    
    async init() {
        await this.showLoadingScreen();
        this.setupScene();
        this.createPlayer();
        this.createEnvironment();
        this.createInteractables();
        this.setupControls();
        this.setupCustomCursor();
        this.animate();
        this.hideLoadingScreen();
        this.addLogEntry("🚀 Space Explorer initialized successfully", "success");
        this.addLogEntry("Navigate through space to discover amazing projects!", "info");
    }
    
    showLoadingScreen() {
        return new Promise((resolve) => {
            const loadingBar = document.getElementById('loadingBar');
            let progress = 0;
            
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;
                
                loadingBar.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(resolve, 500);
                }
            }, 100);
        });
    }
    
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
            loadingScreen.style.display = 'none';
        }, 1000);
    }
    
    setupScene() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x000011, 50, 200);
        
        // Create camera
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.camera.position.set(0, 0, 0);
        
        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x000011);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        document.getElementById('gameContainer').appendChild(this.renderer.domElement);
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }
    
    createPlayer() {
        // Player is just the camera in first person
        this.player = {
            position: this.camera.position,
            rotation: this.camera.rotation
        };
    }
    
    createEnvironment() {
        // Create starfield
        this.createStarfield();
        
        // Create ambient lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);
        
        // Create directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);
        
        // Create nebula effect
        this.createNebula();
    }
    
    createStarfield() {
        const starGeometry = new THREE.BufferGeometry();
        const starCount = 2000;
        const positions = new Float32Array(starCount * 3);
        
        for (let i = 0; i < starCount * 3; i += 3) {
            positions[i] = (Math.random() - 0.5) * 400;
            positions[i + 1] = (Math.random() - 0.5) * 400;
            positions[i + 2] = (Math.random() - 0.5) * 400;
        }
        
        starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        
        const starMaterial = new THREE.PointsMaterial({
            color: 0xffffff,
            size: 0.5,
            transparent: true,
            opacity: 0.8
        });
        
        const stars = new THREE.Points(starGeometry, starMaterial);
        this.scene.add(stars);
        this.stars.push(stars);
    }
    
    createNebula() {
        const nebulaGeometry = new THREE.SphereGeometry(150, 32, 32);
        const nebulaMaterial = new THREE.MeshBasicMaterial({
            color: 0x4a0e4e,
            transparent: true,
            opacity: 0.1,
            side: THREE.BackSide
        });

        const nebula = new THREE.Mesh(nebulaGeometry, nebulaMaterial);
        this.scene.add(nebula);
    }

    createInteractables() {
        // Create project objects
        this.portfolioData.projects.forEach((project, index) => {
            const geometry = new THREE.OctahedronGeometry(2, 0);
            const material = new THREE.MeshPhongMaterial({
                color: project.color,
                emissive: project.color,
                emissiveIntensity: 0.3,
                transparent: true,
                opacity: 0.8
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(project.position.x, project.position.y, project.position.z);
            mesh.userData = { type: 'project', data: project, index };

            // Add glow effect
            const glowGeometry = new THREE.OctahedronGeometry(3, 0);
            const glowMaterial = new THREE.MeshBasicMaterial({
                color: project.color,
                transparent: true,
                opacity: 0.2
            });
            const glow = new THREE.Mesh(glowGeometry, glowMaterial);
            glow.position.copy(mesh.position);

            this.scene.add(mesh);
            this.scene.add(glow);
            this.interactables.push(mesh);

            // Add to minimap
            this.addToMinimap(project.position, 'project');
        });

        // Create skill objects
        this.portfolioData.skills.forEach((skill, index) => {
            const geometry = new THREE.SphereGeometry(1, 16, 16);
            const material = new THREE.MeshPhongMaterial({
                color: skill.color,
                emissive: skill.color,
                emissiveIntensity: 0.2,
                transparent: true,
                opacity: 0.9
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(skill.position.x, skill.position.y, skill.position.z);
            mesh.userData = { type: 'skill', data: skill, index };

            this.scene.add(mesh);
            this.interactables.push(mesh);

            // Add to minimap
            this.addToMinimap(skill.position, 'skill');
        });
    }

    setupControls() {
        // Keyboard controls
        document.addEventListener('keydown', (event) => {
            switch(event.code) {
                case 'KeyW':
                    this.controls.forward = true;
                    break;
                case 'KeyS':
                    this.controls.backward = true;
                    break;
                case 'KeyA':
                    this.controls.left = true;
                    break;
                case 'KeyD':
                    this.controls.right = true;
                    break;
                case 'ShiftLeft':
                    this.controls.boost = true;
                    break;
                case 'Space':
                    event.preventDefault();
                    this.interact();
                    break;
                case 'Escape':
                    this.closeModal();
                    break;
            }
        });

        document.addEventListener('keyup', (event) => {
            switch(event.code) {
                case 'KeyW':
                    this.controls.forward = false;
                    break;
                case 'KeyS':
                    this.controls.backward = false;
                    break;
                case 'KeyA':
                    this.controls.left = false;
                    break;
                case 'KeyD':
                    this.controls.right = false;
                    break;
                case 'ShiftLeft':
                    this.controls.boost = false;
                    break;
            }
        });

        // Mouse controls
        document.addEventListener('mousemove', (event) => {
            this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

            // Update camera rotation based on mouse movement
            if (document.pointerLockElement === this.renderer.domElement) {
                const sensitivity = 0.002;
                this.camera.rotation.y -= event.movementX * sensitivity;
                this.camera.rotation.x -= event.movementY * sensitivity;
                this.camera.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.camera.rotation.x));
            }
        });

        // Pointer lock for mouse look
        this.renderer.domElement.addEventListener('click', () => {
            this.renderer.domElement.requestPointerLock();
        });
    }

    setupCustomCursor() {
        const cursor = document.getElementById('customCursor');

        document.addEventListener('mousemove', (e) => {
            cursor.style.left = e.clientX + 'px';
            cursor.style.top = e.clientY + 'px';
        });

        document.addEventListener('mousedown', () => {
            cursor.style.transform = 'scale(0.8)';
        });

        document.addEventListener('mouseup', () => {
            cursor.style.transform = 'scale(1)';
        });
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        this.updateMovement();
        this.updateInteractables();
        this.updateMinimap();
        this.checkNearbyObjects();

        this.renderer.render(this.scene, this.camera);
    }

    updateMovement() {
        const speed = this.controls.boost ? 0.8 : 0.3;

        this.direction.set(0, 0, 0);

        if (this.controls.forward) this.direction.z -= 1;
        if (this.controls.backward) this.direction.z += 1;
        if (this.controls.left) this.direction.x -= 1;
        if (this.controls.right) this.direction.x += 1;

        this.direction.normalize();
        this.direction.multiplyScalar(speed);

        // Apply camera rotation to movement direction
        this.direction.applyQuaternion(this.camera.quaternion);

        this.camera.position.add(this.direction);

        // Update exploration progress
        const distance = this.camera.position.length();
        this.explorationProgress = Math.min(100, (distance / 100) * 100);
        this.updateUI();
    }

    updateInteractables() {
        const time = Date.now() * 0.001;

        this.interactables.forEach((object, index) => {
            if (object.userData.type === 'project') {
                object.rotation.y += 0.01;
                object.position.y += Math.sin(time + index) * 0.02;
            } else if (object.userData.type === 'skill') {
                object.rotation.x += 0.02;
                object.rotation.y += 0.01;
                object.position.y += Math.cos(time + index) * 0.015;
            }
        });

        // Animate stars
        this.stars.forEach(starField => {
            starField.rotation.y += 0.0002;
        });
    }

    checkNearbyObjects() {
        const playerPosition = this.camera.position;

        this.interactables.forEach(object => {
            const distance = playerPosition.distanceTo(object.position);

            if (distance < 5 && !object.userData.collected) {
                // Highlight nearby objects
                object.material.emissiveIntensity = 0.6;

                if (distance < 3) {
                    // Auto-collect skills
                    if (object.userData.type === 'skill') {
                        this.collectSkill(object);
                    }
                }
            } else {
                object.material.emissiveIntensity = object.userData.type === 'project' ? 0.3 : 0.2;
            }
        });
    }

    interact() {
        const playerPosition = this.camera.position;
        let nearestObject = null;
        let nearestDistance = Infinity;

        this.interactables.forEach(object => {
            if (!object.userData.collected) {
                const distance = playerPosition.distanceTo(object.position);
                if (distance < 8 && distance < nearestDistance) {
                    nearestDistance = distance;
                    nearestObject = object;
                }
            }
        });

        if (nearestObject) {
            if (nearestObject.userData.type === 'project') {
                this.showProjectModal(nearestObject.userData.data);
                this.collectProject(nearestObject);
            } else if (nearestObject.userData.type === 'skill') {
                this.collectSkill(nearestObject);
            }
        }
    }

    collectProject(object) {
        if (object.userData.collected) return;

        object.userData.collected = true;
        this.projectsFound++;

        // Create collection effect
        this.createCollectionEffect(object.position, object.userData.data.color);

        // Hide the object
        object.visible = false;

        this.addLogEntry(`🎯 Project discovered: ${object.userData.data.title}`, "success");
        this.updateUI();
    }

    collectSkill(object) {
        if (object.userData.collected) return;

        object.userData.collected = true;
        this.skillsFound++;

        // Create collection effect
        this.createCollectionEffect(object.position, object.userData.data.color);

        // Hide the object
        object.visible = false;

        this.addLogEntry(`⚡ Skill acquired: ${object.userData.data.name}`, "success");
        this.updateUI();

        if (this.skillsFound === this.totalSkills && this.projectsFound === this.totalProjects) {
            this.addLogEntry("🎉 Mission Complete! All projects and skills discovered!", "success");
        }
    }

    createCollectionEffect(position, color) {
        // Create particle burst effect
        const particleCount = 20;
        const particles = [];

        for (let i = 0; i < particleCount; i++) {
            const geometry = new THREE.SphereGeometry(0.1, 8, 8);
            const material = new THREE.MeshBasicMaterial({ color: color });
            const particle = new THREE.Mesh(geometry, material);

            particle.position.copy(position);
            particle.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            );

            this.scene.add(particle);
            particles.push(particle);
        }

        // Animate particles
        const animateParticles = () => {
            particles.forEach((particle, index) => {
                particle.position.add(particle.velocity);
                particle.velocity.multiplyScalar(0.95);
                particle.material.opacity -= 0.02;

                if (particle.material.opacity <= 0) {
                    this.scene.remove(particle);
                    particles.splice(index, 1);
                }
            });

            if (particles.length > 0) {
                requestAnimationFrame(animateParticles);
            }
        };

        animateParticles();
    }

    showProjectModal(project) {
        const modal = document.getElementById('gameModal');
        const title = document.getElementById('modalTitle');
        const description = document.getElementById('modalDescription');
        const techGrid = document.getElementById('modalTech');
        const liveBtn = document.getElementById('liveBtn');
        const codeBtn = document.getElementById('codeBtn');

        title.textContent = project.title;
        description.textContent = project.description;

        techGrid.innerHTML = '';
        project.technologies.forEach(tech => {
            const techItem = document.createElement('div');
            techItem.className = 'tech-item';
            techItem.textContent = tech;
            techGrid.appendChild(techItem);
        });

        liveBtn.href = project.liveUrl;
        codeBtn.href = project.codeUrl;

        modal.style.display = 'flex';
    }

    closeModal() {
        const modal = document.getElementById('gameModal');
        modal.style.display = 'none';
    }

    updateUI() {
        document.getElementById('projectCount').textContent = `${this.projectsFound}/${this.totalProjects}`;
        document.getElementById('skillCount').textContent = `${this.skillsFound}/${this.totalSkills}`;
        document.getElementById('explorationPercent').textContent = `${Math.round(this.explorationProgress)}%`;

        const projectProgress = (this.projectsFound / this.totalProjects) * 100;
        const skillProgress = (this.skillsFound / this.totalSkills) * 100;

        document.getElementById('projectProgress').style.width = `${projectProgress}%`;
        document.getElementById('skillProgress').style.width = `${skillProgress}%`;
        document.getElementById('explorationProgress').style.width = `${this.explorationProgress}%`;
    }

    addLogEntry(message, type = "info") {
        const logContainer = document.getElementById('logContainer');
        const entry = document.createElement('div');
        entry.className = `log-entry ${type}`;
        entry.textContent = message;

        logContainer.appendChild(entry);
        logContainer.scrollTop = logContainer.scrollHeight;

        // Keep only last 10 entries
        while (logContainer.children.length > 10) {
            logContainer.removeChild(logContainer.firstChild);
        }
    }

    addToMinimap(position, type) {
        const minimap = document.getElementById('minimapContent');
        const dot = document.createElement('div');
        dot.className = `minimap-dot ${type}`;

        // Convert 3D position to 2D minimap coordinates
        const x = ((position.x + 100) / 200) * 180;
        const y = ((position.z + 100) / 200) * 130;

        dot.style.left = `${Math.max(0, Math.min(180, x))}px`;
        dot.style.top = `${Math.max(0, Math.min(130, y))}px`;

        minimap.appendChild(dot);
    }

    updateMinimap() {
        // Update player position on minimap
        const playerDot = document.querySelector('.minimap-dot.player');
        if (!playerDot) {
            const dot = document.createElement('div');
            dot.className = 'minimap-dot player';
            document.getElementById('minimapContent').appendChild(dot);
        }

        const playerPos = this.camera.position;
        const x = ((playerPos.x + 100) / 200) * 180;
        const y = ((playerPos.z + 100) / 200) * 130;

        const playerDotElement = document.querySelector('.minimap-dot.player');
        if (playerDotElement) {
            playerDotElement.style.left = `${Math.max(0, Math.min(180, x))}px`;
            playerDotElement.style.top = `${Math.max(0, Math.min(130, y))}px`;
        }
    }
}

// Global functions
function closeModal() {
    if (window.spaceExplorer) {
        window.spaceExplorer.closeModal();
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.spaceExplorer = new SpaceExplorer();
});
