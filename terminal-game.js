// Terminal Quest - Lightweight Portfolio Game
class TerminalGame {
    constructor() {
        this.mapWidth = 10;
        this.mapHeight = 8;
        this.player = { x: 0, y: 0 };
        this.visitedCells = new Set();
        this.projectsFound = 0;
        this.skillsFound = 0;
        this.totalProjects = 6;
        this.totalSkills = 12;
        
        this.gameData = {
            projects: [
                {
                    x: 2, y: 1,
                    title: "E-commerce Platform",
                    description: "Full-stack e-commerce solution with React frontend and Node.js backend. Features include user authentication, payment processing, inventory management, and admin dashboard.",
                    technologies: ["React", "Node.js", "MongoDB", "Stripe", "JWT"],
                    liveUrl: "https://demo-ecommerce.com",
                    codeUrl: "https://github.com/user/ecommerce"
                },
                {
                    x: 7, y: 2,
                    title: "Task Management App",
                    description: "Collaborative task management application with real-time updates, drag-and-drop functionality, team collaboration features, and progress tracking.",
                    technologies: ["Vue.js", "Socket.io", "Express", "PostgreSQL"],
                    liveUrl: "https://taskmanager-demo.com",
                    codeUrl: "https://github.com/user/taskmanager"
                },
                {
                    x: 4, y: 4,
                    title: "Weather Dashboard",
                    description: "Interactive weather dashboard with location-based forecasts, historical data visualization, and severe weather alerts using multiple weather APIs.",
                    technologies: ["JavaScript", "Chart.js", "Weather API", "CSS3"],
                    liveUrl: "https://weather-dash.com",
                    codeUrl: "https://github.com/user/weather-dashboard"
                },
                {
                    x: 1, y: 6,
                    title: "Portfolio Website",
                    description: "Responsive portfolio website with modern design, smooth animations, contact form, and blog functionality. Built with performance and SEO in mind.",
                    technologies: ["HTML5", "CSS3", "JavaScript", "GSAP"],
                    liveUrl: "https://myportfolio.com",
                    codeUrl: "https://github.com/user/portfolio"
                },
                {
                    x: 8, y: 5,
                    title: "API Gateway Service",
                    description: "Microservices API gateway with rate limiting, authentication, request routing, and monitoring. Handles thousands of requests per second.",
                    technologies: ["Go", "Redis", "Docker", "Kubernetes"],
                    liveUrl: "https://api-gateway-demo.com",
                    codeUrl: "https://github.com/user/api-gateway"
                },
                {
                    x: 5, y: 7,
                    title: "Machine Learning Model",
                    description: "Predictive analytics model for customer behavior analysis with data preprocessing, feature engineering, and model deployment pipeline.",
                    technologies: ["Python", "TensorFlow", "Pandas", "FastAPI"],
                    liveUrl: "https://ml-demo.com",
                    codeUrl: "https://github.com/user/ml-model"
                }
            ],
            skills: [
                { x: 1, y: 1, name: "JavaScript", icon: "JS" },
                { x: 3, y: 2, name: "React", icon: "⚛️" },
                { x: 6, y: 1, name: "Node.js", icon: "🟢" },
                { x: 9, y: 3, name: "Python", icon: "🐍" },
                { x: 2, y: 4, name: "Docker", icon: "🐳" },
                { x: 7, y: 4, name: "AWS", icon: "☁️" },
                { x: 0, y: 5, name: "MongoDB", icon: "🍃" },
                { x: 3, y: 6, name: "PostgreSQL", icon: "🐘" },
                { x: 6, y: 6, name: "Git", icon: "📝" },
                { x: 9, y: 6, name: "Linux", icon: "🐧" },
                { x: 4, y: 2, name: "TypeScript", icon: "TS" },
                { x: 8, y: 7, name: "GraphQL", icon: "📊" }
            ],
            walls: [
                { x: 3, y: 3 }, { x: 4, y: 3 }, { x: 5, y: 3 },
                { x: 6, y: 3 }, { x: 7, y: 3 }
            ]
        };
        
        this.init();
    }
    
    init() {
        this.createMap();
        this.updateStats();
        this.setupControls();
        this.addLogEntry("Game initialized successfully", "success");
        this.addLogEntry("Find all projects (💜) and skills (💛) to complete the quest!", "info");
    }
    
    createMap() {
        const mapElement = document.getElementById('gameMap');
        mapElement.innerHTML = '';
        
        for (let y = 0; y < this.mapHeight; y++) {
            for (let x = 0; x < this.mapWidth; x++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.dataset.x = x;
                cell.dataset.y = y;
                
                // Set cell content and type
                if (x === this.player.x && y === this.player.y) {
                    cell.className += ' player';
                    cell.textContent = '@';
                } else if (this.isWall(x, y)) {
                    cell.className += ' wall';
                    cell.textContent = '█';
                } else if (this.isProject(x, y)) {
                    cell.className += ' project';
                    cell.textContent = '💜';
                } else if (this.isSkill(x, y)) {
                    cell.className += ' skill';
                    const skill = this.getSkill(x, y);
                    cell.textContent = skill ? skill.icon : '💛';
                } else {
                    cell.textContent = '·';
                }
                
                if (this.visitedCells.has(`${x},${y}`)) {
                    cell.className += ' visited';
                }
                
                cell.addEventListener('click', () => this.moveToCell(x, y));
                mapElement.appendChild(cell);
            }
        }
    }
    
    setupControls() {
        document.addEventListener('keydown', (e) => {
            switch(e.key.toLowerCase()) {
                case 'w':
                case 'arrowup':
                    e.preventDefault();
                    this.movePlayer(0, -1);
                    break;
                case 's':
                case 'arrowdown':
                    e.preventDefault();
                    this.movePlayer(0, 1);
                    break;
                case 'a':
                case 'arrowleft':
                    e.preventDefault();
                    this.movePlayer(-1, 0);
                    break;
                case 'd':
                case 'arrowright':
                    e.preventDefault();
                    this.movePlayer(1, 0);
                    break;
                case ' ':
                    e.preventDefault();
                    this.interact();
                    break;
                case 'escape':
                    e.preventDefault();
                    this.closeModal();
                    break;
                case 'r':
                    e.preventDefault();
                    this.resetGame();
                    break;
            }
        });
    }
    
    movePlayer(dx, dy) {
        const newX = Math.max(0, Math.min(this.mapWidth - 1, this.player.x + dx));
        const newY = Math.max(0, Math.min(this.mapHeight - 1, this.player.y + dy));
        
        if (!this.isWall(newX, newY)) {
            this.player.x = newX;
            this.player.y = newY;
            this.visitedCells.add(`${newX},${newY}`);
            this.createMap();
            this.updateStats();
            this.checkInteraction();
            
            this.addLogEntry(`Moved to (${newX}, ${newY})`, "info");
        } else {
            this.addLogEntry("Cannot move through walls!", "warning");
        }
    }
    
    moveToCell(x, y) {
        if (!this.isWall(x, y)) {
            this.player.x = x;
            this.player.y = y;
            this.visitedCells.add(`${x},${y}`);
            this.createMap();
            this.updateStats();
            this.checkInteraction();
            
            this.addLogEntry(`Teleported to (${x}, ${y})`, "info");
        }
    }
    
    checkInteraction() {
        const { x, y } = this.player;
        
        if (this.isProject(x, y)) {
            const project = this.getProject(x, y);
            if (project) {
                this.showProjectModal(project);
                this.projectsFound++;
                this.addLogEntry(`Discovered project: ${project.title}`, "success");
            }
        } else if (this.isSkill(x, y)) {
            const skill = this.getSkill(x, y);
            if (skill) {
                this.skillsFound++;
                this.addLogEntry(`Acquired skill: ${skill.name}`, "success");
                this.checkWinCondition();
            }
        }
    }
    
    interact() {
        this.checkInteraction();
    }
    
    isWall(x, y) {
        return this.gameData.walls.some(wall => wall.x === x && wall.y === y);
    }
    
    isProject(x, y) {
        return this.gameData.projects.some(project => project.x === x && project.y === y);
    }
    
    isSkill(x, y) {
        return this.gameData.skills.some(skill => skill.x === x && skill.y === y);
    }
    
    getProject(x, y) {
        return this.gameData.projects.find(project => project.x === x && project.y === y);
    }
    
    getSkill(x, y) {
        return this.gameData.skills.find(skill => skill.x === x && skill.y === y);
    }
    
    showProjectModal(project) {
        const modal = document.getElementById('gameModal');
        const title = document.getElementById('modalTitle');
        const description = document.getElementById('modalDescription');
        const techTags = document.getElementById('modalTech');
        const liveBtn = document.getElementById('liveBtn');
        const codeBtn = document.getElementById('codeBtn');
        
        title.textContent = project.title;
        description.textContent = project.description;
        
        techTags.innerHTML = '';
        project.technologies.forEach(tech => {
            const tag = document.createElement('span');
            tag.className = 'tech-tag';
            tag.textContent = tech;
            techTags.appendChild(tag);
        });
        
        liveBtn.href = project.liveUrl;
        codeBtn.href = project.codeUrl;
        
        modal.style.display = 'flex';
    }
    
    closeModal() {
        const modal = document.getElementById('gameModal');
        modal.style.display = 'none';
    }
    
    updateStats() {
        document.getElementById('playerPos').textContent = `${this.player.x},${this.player.y}`;
        document.getElementById('projectsFound').textContent = `${this.projectsFound}/${this.totalProjects}`;
        document.getElementById('skillsFound').textContent = `${this.skillsFound}/${this.totalSkills}`;
        
        const progress = ((this.projectsFound + this.skillsFound) / (this.totalProjects + this.totalSkills)) * 100;
        document.getElementById('progressBar').style.width = `${progress}%`;
    }
    
    addLogEntry(message, type = "info") {
        const log = document.getElementById('gameLog');
        const entry = document.createElement('div');
        entry.className = `log-entry ${type}`;
        entry.textContent = `> ${message}`;
        
        log.appendChild(entry);
        log.scrollTop = log.scrollHeight;
        
        // Keep only last 20 entries
        while (log.children.length > 20) {
            log.removeChild(log.firstChild);
        }
    }
    
    checkWinCondition() {
        if (this.projectsFound >= this.totalProjects && this.skillsFound >= this.totalSkills) {
            this.addLogEntry("🎉 QUEST COMPLETED! All projects and skills discovered!", "success");
            this.addLogEntry("Thank you for exploring my portfolio!", "success");
        }
    }
    
    resetGame() {
        this.player = { x: 0, y: 0 };
        this.visitedCells.clear();
        this.projectsFound = 0;
        this.skillsFound = 0;
        this.createMap();
        this.updateStats();
        this.closeModal();
        
        const log = document.getElementById('gameLog');
        log.innerHTML = '';
        this.addLogEntry("Game reset successfully", "success");
        this.addLogEntry("Welcome back to Terminal Quest!", "info");
    }
}

// Global functions
function closeModal() {
    game.closeModal();
}

// Initialize game when page loads
let game;
document.addEventListener('DOMContentLoaded', () => {
    game = new TerminalGame();
});
