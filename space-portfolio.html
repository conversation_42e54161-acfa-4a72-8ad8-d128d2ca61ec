<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Space Explorer - Interactive Portfolio</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Orbitron', monospace;
            background: #000;
            color: #fff;
            overflow: hidden;
            cursor: none;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #customCursor {
            position: fixed;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #00ffff 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            transition: transform 0.1s ease;
        }
        
        .hud {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 20, 40, 0.8);
            border: 2px solid #00ffff;
            border-radius: 10px;
            padding: 20px;
            backdrop-filter: blur(10px);
            min-width: 250px;
        }
        
        .hud h2 {
            color: #00ffff;
            margin-bottom: 15px;
            font-size: 1.2rem;
            text-shadow: 0 0 10px #00ffff;
        }
        
        .stat-bar {
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .progress-container {
            width: 150px;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-left: 10px;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
            border-radius: 4px;
            transition: width 0.5s ease;
            box-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
        }
        
        .mission-log {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 20, 40, 0.8);
            border: 2px solid #00ffff;
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(10px);
            max-width: 400px;
            max-height: 150px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin: 5px 0;
            font-size: 0.9rem;
            opacity: 0;
            animation: fadeIn 0.5s ease forwards;
        }
        
        .log-entry.success {
            color: #4ecdc4;
            text-shadow: 0 0 5px #4ecdc4;
        }
        
        .log-entry.warning {
            color: #ffd93d;
            text-shadow: 0 0 5px #ffd93d;
        }
        
        .log-entry.info {
            color: #74b9ff;
            text-shadow: 0 0 5px #74b9ff;
        }
        
        @keyframes fadeIn {
            to { opacity: 1; }
        }
        
        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
            background: rgba(0, 20, 40, 0.8);
            border: 2px solid #00ffff;
            border-radius: 10px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .controls h3 {
            color: #00ffff;
            margin-bottom: 10px;
            text-shadow: 0 0 10px #00ffff;
        }
        
        .control-item {
            margin: 8px 0;
            font-size: 0.9rem;
            display: flex;
            justify-content: space-between;
            min-width: 200px;
        }
        
        .key {
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 8px;
            border-radius: 4px;
            border: 1px solid #00ffff;
            font-family: 'Orbitron', monospace;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background: linear-gradient(135deg, #0f3460 0%, #16537e 100%);
            border: 3px solid #00ffff;
            border-radius: 15px;
            padding: 40px;
            max-width: 700px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 0 50px rgba(0, 255, 255, 0.3);
        }
        
        .modal-content h2 {
            color: #00ffff;
            margin-bottom: 20px;
            font-size: 2rem;
            text-shadow: 0 0 20px #00ffff;
            text-align: center;
        }
        
        .modal-content p {
            margin: 15px 0;
            line-height: 1.8;
            font-size: 1.1rem;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .tech-item {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid #00ffff;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .tech-item:hover {
            background: rgba(0, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3);
        }
        
        .modal-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.5);
        }
        
        .btn.secondary {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.3);
        }
        
        .btn.secondary:hover {
            box-shadow: 0 10px 25px rgba(116, 185, 255, 0.5);
        }
        
        .close-btn {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            color: #ff6b6b;
            font-size: 2rem;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            transition: all 0.3s ease;
        }
        
        .close-btn:hover {
            color: #fff;
            text-shadow: 0 0 10px #ff6b6b;
            transform: scale(1.2);
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #000428 0%, #004e92 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            transition: opacity 1s ease;
        }
        
        .loading-text {
            font-size: 2.5rem;
            color: #00ffff;
            margin-bottom: 30px;
            text-shadow: 0 0 20px #00ffff;
            animation: pulse 2s infinite;
        }
        
        .loading-bar-container {
            width: 400px;
            height: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
            border: 2px solid #00ffff;
        }
        
        .loading-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
            border-radius: 5px;
            width: 0%;
            transition: width 0.3s ease;
            box-shadow: 0 0 20px rgba(78, 205, 196, 0.8);
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .minimap {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 200px;
            height: 150px;
            background: rgba(0, 20, 40, 0.8);
            border: 2px solid #00ffff;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            z-index: 100;
        }
        
        .minimap-content {
            position: relative;
            width: 100%;
            height: 100%;
            padding: 10px;
        }
        
        .minimap-dot {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .minimap-dot.player {
            background: #00ffff;
            box-shadow: 0 0 10px #00ffff;
        }
        
        .minimap-dot.project {
            background: #ff6b6b;
            box-shadow: 0 0 8px #ff6b6b;
        }
        
        .minimap-dot.skill {
            background: #ffd93d;
            box-shadow: 0 0 8px #ffd93d;
        }
        
        @media (max-width: 768px) {
            .hud, .controls, .mission-log {
                font-size: 0.8rem;
                padding: 15px;
            }
            
            .minimap {
                width: 150px;
                height: 100px;
            }
            
            .modal-content {
                padding: 20px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div id="customCursor"></div>
    
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-text">INITIALIZING SPACE EXPLORER</div>
        <div class="loading-bar-container">
            <div class="loading-bar-fill" id="loadingBar"></div>
        </div>
        <p style="margin-top: 20px; color: #74b9ff;">Preparing your journey through the digital cosmos...</p>
    </div>
    
    <div id="gameContainer"></div>
    
    <div class="hud">
        <h2>MISSION STATUS</h2>
        <div class="stat-bar">
            <span>Projects Discovered:</span>
            <span id="projectCount">0/8</span>
        </div>
        <div class="progress-container">
            <div class="progress-bar" id="projectProgress" style="width: 0%"></div>
        </div>
        
        <div class="stat-bar">
            <span>Skills Acquired:</span>
            <span id="skillCount">0/15</span>
        </div>
        <div class="progress-container">
            <div class="progress-bar" id="skillProgress" style="width: 0%"></div>
        </div>
        
        <div class="stat-bar">
            <span>Exploration:</span>
            <span id="explorationPercent">0%</span>
        </div>
        <div class="progress-container">
            <div class="progress-bar" id="explorationProgress" style="width: 0%"></div>
        </div>
    </div>
    
    <div class="controls">
        <h3>SHIP CONTROLS</h3>
        <div class="control-item">
            <span>Move Forward</span>
            <span class="key">W</span>
        </div>
        <div class="control-item">
            <span>Move Backward</span>
            <span class="key">S</span>
        </div>
        <div class="control-item">
            <span>Strafe Left/Right</span>
            <span class="key">A/D</span>
        </div>
        <div class="control-item">
            <span>Look Around</span>
            <span class="key">MOUSE</span>
        </div>
        <div class="control-item">
            <span>Boost</span>
            <span class="key">SHIFT</span>
        </div>
        <div class="control-item">
            <span>Interact</span>
            <span class="key">SPACE</span>
        </div>
    </div>
    
    <div class="mission-log">
        <div id="logContainer">
            <div class="log-entry info">🚀 Welcome to Space Explorer Portfolio</div>
            <div class="log-entry info">Navigate through space to discover projects and skills</div>
            <div class="log-entry warning">Use WASD to move, mouse to look around</div>
        </div>
    </div>
    
    <div class="minimap">
        <div class="minimap-content" id="minimapContent">
            <div style="color: #00ffff; font-size: 0.8rem; margin-bottom: 5px;">RADAR</div>
        </div>
    </div>
    
    <div class="modal" id="gameModal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeModal()">&times;</button>
            <h2 id="modalTitle">Project Title</h2>
            <p id="modalDescription">Project description goes here...</p>
            <div class="tech-grid" id="modalTech"></div>
            <div class="modal-buttons">
                <a href="#" class="btn" id="liveBtn" target="_blank">🚀 Launch Project</a>
                <a href="#" class="btn secondary" id="codeBtn" target="_blank">📝 View Code</a>
            </div>
        </div>
    </div>
    
    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="space-game.js"></script>
</body>
</html>
