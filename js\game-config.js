// Game configuration for The Developer's Quest
const GAME_CONFIG = {
    // Game dimensions
    width: 1024,
    height: 576,
    
    // Physics settings
    physics: {
        default: 'arcade',
        arcade: {
            gravity: { y: 800 },
            debug: false
        }
    },
    
    // Rendering settings
    type: Phaser.AUTO,
    backgroundColor: '#1a1a2e',
    pixelArt: true,
    
    // Player settings
    player: {
        speed: 200,
        jumpVelocity: -500,
        size: { width: 32, height: 48 },
        startPosition: { x: 100, y: 400 }
    },
    
    // Collectible settings
    collectibles: {
        skillCoin: {
            size: 24,
            bounceHeight: 10,
            rotationSpeed: 0.02,
            collectSound: 'coin-collect'
        },
        projectCrystal: {
            size: 48,
            glowIntensity: 0.8,
            pulseSpeed: 0.03,
            collectSound: 'crystal-collect'
        }
    },
    
    // World settings
    worlds: {
        hub: {
            backgroundColor: '#2d4a87',
            music: 'hub-theme'
        },
        frontend: {
            backgroundColor: '#2d5a27',
            music: 'forest-theme'
        },
        backend: {
            backgroundColor: '#3d2a1a',
            music: 'cave-theme'
        },
        cloud: {
            backgroundColor: '#4a4a6a',
            music: 'sky-theme'
        }
    },
    
    // UI settings
    ui: {
        hudStyle: {
            fontSize: '16px',
            fontFamily: 'Courier New',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 2
        },
        modalStyle: {
            backgroundColor: 0x16213e,
            borderColor: 0x00ffff,
            borderWidth: 3,
            padding: 20
        }
    },
    
    // Audio settings
    audio: {
        masterVolume: 0.7,
        musicVolume: 0.5,
        sfxVolume: 0.8
    },
    
    // Input settings
    input: {
        keyboard: {
            left: ['LEFT', 'A'],
            right: ['RIGHT', 'D'],
            jump: ['SPACE', 'UP', 'W'],
            interact: ['E', 'ENTER'],
            pause: ['ESC', 'P']
        },
        mobile: {
            enabled: true,
            buttonSize: 60,
            buttonOpacity: 0.7
        }
    },
    
    // Animation settings
    animations: {
        player: {
            idle: { frameRate: 8, repeat: -1 },
            run: { frameRate: 12, repeat: -1 },
            jump: { frameRate: 8, repeat: 0 },
            fall: { frameRate: 8, repeat: 0 }
        },
        collectibles: {
            skillCoin: { frameRate: 10, repeat: -1 },
            projectCrystal: { frameRate: 6, repeat: -1 }
        }
    },
    
    // Level design constants
    level: {
        tileSize: 32,
        platformHeight: 32,
        checkpointDistance: 500,
        worldWidth: 1600,
        worldHeight: 600
    },
    
    // Game state
    gameState: {
        currentWorld: 'hub',
        skillCoinsCollected: 0,
        projectCrystalsCollected: 0,
        unlockedWorlds: ['hub'],
        completedProjects: [],
        playerPosition: { x: 100, y: 400 }
    }
};

// Scene configuration
const SCENE_CONFIG = {
    preload: 'PreloadScene',
    hub: 'HubScene',
    frontend: 'FrontendScene',
    backend: 'BackendScene',
    cloud: 'CloudScene'
};

// Export configurations
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GAME_CONFIG, SCENE_CONFIG };
}
