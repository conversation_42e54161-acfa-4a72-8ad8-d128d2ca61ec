// Frontend Forest Scene for The Developer's Quest
class FrontendScene extends Phaser.Scene {
    constructor() {
        super({ key: 'FrontendScene' });
        this.player = null;
        this.platforms = null;
        this.collectibleManager = null;
        this.backgroundElements = null;
        this.exitPortal = null;
    }
    
    create() {
        console.log('FrontendScene: Entering Frontend Forest');
        
        // Set world bounds
        this.physics.world.setBounds(0, 0, GAME_CONFIG.level.worldWidth, GAME_CONFIG.level.worldHeight);
        
        // Create background
        this.createBackground();
        
        // Create platforms and world geometry
        this.createPlatforms();
        
        // Create player
        this.createPlayer();
        
        // Create collectibles
        this.createCollectibles();
        
        // Create interactive elements
        this.createInteractiveElements();
        
        // Create exit portal
        this.createExitPortal();
        
        // Setup camera
        this.setupCamera();
        
        // Setup input
        this.setupInput();
        
        // Update game state
        GAME_CONFIG.gameState.currentWorld = 'frontend';
    }
    
    createBackground() {
        // Create forest background with gradient
        const graphics = this.add.graphics();
        graphics.fillGradientStyle(0x2d5a27, 0x2d5a27, 0x1a3d1a, 0x1a3d1a, 1);
        graphics.fillRect(0, 0, GAME_CONFIG.level.worldWidth, GAME_CONFIG.level.worldHeight);
        
        // Add background trees and foliage
        this.backgroundElements = this.add.group();
        
        for (let i = 0; i < 30; i++) {
            const x = Phaser.Math.Between(0, GAME_CONFIG.level.worldWidth);
            const y = Phaser.Math.Between(400, GAME_CONFIG.level.worldHeight - 50);
            
            const tree = this.add.image(x, y, 'tree');
            tree.setScale(Phaser.Math.FloatBetween(0.8, 1.5));
            tree.setTint(Phaser.Math.Between(0x228B22, 0x32CD32));
            tree.setAlpha(0.7);
            this.backgroundElements.add(tree);
        }
        
        // Add floating leaves
        for (let i = 0; i < 15; i++) {
            const x = Phaser.Math.Between(0, GAME_CONFIG.level.worldWidth);
            const y = Phaser.Math.Between(0, 300);
            
            const leaf = this.add.circle(x, y, 3, 0x90EE90, 0.6);
            
            this.tweens.add({
                targets: leaf,
                x: x + Phaser.Math.Between(-100, 100),
                y: y + Phaser.Math.Between(50, 200),
                duration: Phaser.Math.Between(8000, 15000),
                repeat: -1,
                yoyo: true,
                ease: 'Sine.easeInOut'
            });
        }
    }
    
    createPlatforms() {
        this.platforms = this.physics.add.staticGroup();
        
        // Ground platforms
        for (let x = 0; x < GAME_CONFIG.level.worldWidth; x += 32) {
            const platform = this.platforms.create(x + 16, GAME_CONFIG.level.worldHeight - 16, 'platform');
            platform.setTint(0x8B4513);
            platform.setScale(1).refreshBody();
        }
        
        // Tree-top platforms (bouncy mushrooms concept)
        const platformPositions = [
            { x: 150, y: 450 },
            { x: 300, y: 400 },
            { x: 500, y: 350 },
            { x: 700, y: 300 },
            { x: 900, y: 350 },
            { x: 1100, y: 300 },
            { x: 1300, y: 250 },
            { x: 1500, y: 300 }
        ];
        
        platformPositions.forEach(pos => {
            const platform = this.platforms.create(pos.x, pos.y, 'platform');
            platform.setTint(0x90EE90);
            platform.setScale(1.2, 0.8).refreshBody();
        });
        
        // Vine platforms (climbable concept)
        for (let i = 0; i < 5; i++) {
            const x = 200 + (i * 300);
            for (let j = 0; j < 3; j++) {
                const y = 500 - (j * 80);
                const vine = this.platforms.create(x, y, 'platform');
                vine.setTint(0x228B22);
                vine.setScale(0.5, 2).refreshBody();
            }
        }
    }
    
    createPlayer() {
        const startX = 100;
        const startY = 400;
        
        this.player = new Player(this, startX, startY);
        
        // Setup collision with platforms
        this.physics.add.collider(this.player.sprite, this.platforms);
    }
    
    createCollectibles() {
        this.collectibleManager = new CollectibleManager(this);
        this.collectibleManager.create();
        
        // Load Frontend world collectibles
        this.collectibleManager.loadWorldCollectibles('frontend');
    }
    
    createInteractiveElements() {
        // Create bouncy mushrooms
        this.bouncyMushrooms = this.physics.add.staticGroup();
        
        const mushroomPositions = [
            { x: 250, y: 480 },
            { x: 600, y: 450 },
            { x: 1000, y: 420 },
            { x: 1400, y: 380 }
        ];
        
        mushroomPositions.forEach(pos => {
            const mushroom = this.bouncyMushrooms.create(pos.x, pos.y, 'platform');
            mushroom.setTint(0xFF69B4);
            mushroom.setScale(0.8).refreshBody();
            
            // Add bouncing animation
            this.tweens.add({
                targets: mushroom,
                scaleY: 0.6,
                duration: 1000,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        });
        
        // Setup bouncy mushroom collision
        this.physics.add.collider(this.player.sprite, this.bouncyMushrooms, this.bouncePlayer, null, this);
        
        // Create moving platforms (representing swaying branches)
        this.movingPlatforms = this.physics.add.group();
        
        const movingPlatformData = [
            { x: 400, y: 250, moveDistance: 100, duration: 3000 },
            { x: 800, y: 200, moveDistance: 150, duration: 4000 },
            { x: 1200, y: 180, moveDistance: 80, duration: 2500 }
        ];
        
        movingPlatformData.forEach(data => {
            const platform = this.movingPlatforms.create(data.x, data.y, 'platform');
            platform.setTint(0x8FBC8F);
            platform.body.setImmovable(true);
            platform.body.setSize(64, 16);
            
            this.tweens.add({
                targets: platform,
                x: data.x + data.moveDistance,
                duration: data.duration,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        });
        
        this.physics.add.collider(this.player.sprite, this.movingPlatforms);
    }
    
    createExitPortal() {
        // Portal back to hub
        this.exitPortal = this.physics.add.group();
        const portal = this.exitPortal.create(GAME_CONFIG.level.worldWidth - 100, 400, 'portal');
        portal.setScale(0.6);
        portal.destination = 'HubScene';
        
        // Add portal animation
        this.tweens.add({
            targets: portal,
            scaleX: 0.7,
            scaleY: 0.7,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        this.tweens.add({
            targets: portal,
            rotation: Math.PI * 2,
            duration: 6000,
            repeat: -1,
            ease: 'Linear'
        });
        
        // Setup portal collision
        this.physics.add.overlap(this.player.sprite, this.exitPortal, this.exitWorld, null, this);
    }
    
    setupCamera() {
        // Camera follows player
        this.cameras.main.startFollow(this.player.sprite);
        this.cameras.main.setBounds(0, 0, GAME_CONFIG.level.worldWidth, GAME_CONFIG.level.worldHeight);
        
        // Set camera zoom for better view of the forest
        this.cameras.main.setZoom(1.1);
    }
    
    setupInput() {
        // Pause key
        this.input.keyboard.on('keydown-ESC', () => {
            this.scene.pause();
        });
        
        // Return to hub key
        this.input.keyboard.on('keydown-H', () => {
            this.scene.start('HubScene');
        });
    }
    
    bouncePlayer(player, mushroom) {
        // Give player extra jump boost
        player.body.setVelocityY(-700);
        
        // Create bounce effect
        const effect = this.add.circle(mushroom.x, mushroom.y - 20, 20, 0xFF69B4, 0.6);
        
        this.tweens.add({
            targets: effect,
            scaleX: 2,
            scaleY: 2,
            alpha: 0,
            duration: 300,
            ease: 'Power2',
            onComplete: () => {
                effect.destroy();
            }
        });
        
        // Mushroom compression effect
        this.tweens.add({
            targets: mushroom,
            scaleY: 0.4,
            duration: 100,
            yoyo: true,
            ease: 'Power2'
        });
    }
    
    exitWorld(player, portal) {
        // Add exit effect
        const effect = this.add.circle(portal.x, portal.y, 80, 0x9400D3, 0.5);
        effect.setScale(0);
        
        this.tweens.add({
            targets: effect,
            scaleX: 2,
            scaleY: 2,
            alpha: 0,
            duration: 500,
            ease: 'Power2',
            onComplete: () => {
                effect.destroy();
                this.scene.start(portal.destination);
            }
        });
        
        // Disable player movement
        player.body.setVelocity(0, 0);
        
        console.log('Returning to Hub World');
    }
    
    update(time, delta) {
        if (this.player) {
            this.player.update(delta);
        }
        
        // Update moving platform physics
        if (this.movingPlatforms) {
            this.movingPlatforms.children.entries.forEach(platform => {
                platform.body.velocity.x = 0;
                platform.body.velocity.y = 0;
            });
        }
    }
    
    shutdown() {
        // Clean up when leaving scene
        if (this.collectibleManager) {
            this.collectibleManager.destroy();
        }
    }
}
