// Main game initialization for The Developer's Quest
class GameManager {
    constructor() {
        this.game = null;
        this.isInitialized = false;
    }
    
    init() {
        if (this.isInitialized) return;
        
        console.log('Initializing The Developer\'s Quest...');
        
        // Configure Phaser game
        const config = {
            type: Phaser.AUTO,
            width: GAME_CONFIG.width,
            height: GAME_CONFIG.height,
            parent: 'game-container',
            backgroundColor: GAME_CONFIG.backgroundColor,
            physics: GAME_CONFIG.physics,
            pixelArt: GAME_CONFIG.pixelArt,
            scene: [
                PreloadScene,
                HubScene,
                FrontendScene,
                BackendScene,
                CloudScene
            ],
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH,
                min: {
                    width: 800,
                    height: 450
                },
                max: {
                    width: 1600,
                    height: 900
                }
            },
            input: {
                keyboard: true,
                mouse: true,
                touch: true
            },
            render: {
                antialias: false,
                pixelArt: true,
                roundPixels: true,
                powerPreference: 'high-performance'
            },
            fps: {
                target: 60,
                forceSetTimeOut: true
            }
        };
        
        // Create game instance
        this.game = new Phaser.Game(config);
        
        // Store game reference globally
        window.game = this.game;
        
        // Setup game event listeners
        this.setupGameEvents();
        
        this.isInitialized = true;
        
        console.log('Game initialized successfully!');
    }
    
    setupGameEvents() {
        // Handle game ready
        this.game.events.on('ready', () => {
            console.log('Game is ready!');
            uiManager.handleResize();
        });
        
        // Handle scene transitions
        this.game.events.on('step', () => {
            // Update UI manager with current scene
            const activeScenes = this.game.scene.getScenes(true);
            if (activeScenes.length > 0) {
                uiManager.setCurrentScene(activeScenes[0].scene.key);
            }
        });
        
        // Handle visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Pause game when tab is not visible
                if (this.game && this.game.scene.isActive()) {
                    this.game.scene.pause();
                }
            } else {
                // Resume game when tab becomes visible
                if (this.game && this.game.scene.isPaused()) {
                    this.game.scene.resume();
                }
            }
        });
    }
    
    destroy() {
        if (this.game) {
            this.game.destroy(true);
            this.game = null;
            window.game = null;
            this.isInitialized = false;
        }
    }
    
    restart() {
        this.destroy();
        setTimeout(() => {
            this.init();
        }, 100);
    }
}

// Note: BackendScene and CloudScene are now implemented in separate files

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing game...');
    
    const gameManager = new GameManager();
    gameManager.init();
    
    // Store game manager globally for debugging
    window.gameManager = gameManager;
});

// Handle page unload
window.addEventListener('beforeunload', () => {
    if (window.gameManager) {
        window.gameManager.destroy();
    }
});
