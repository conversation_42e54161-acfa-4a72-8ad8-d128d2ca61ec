// Main game initialization for The Developer's Quest
class GameManager {
    constructor() {
        this.game = null;
        this.isInitialized = false;
    }
    
    init() {
        if (this.isInitialized) return;

        console.log('🎮 Initializing The Developer\'s Quest...');

        try {
            // Configure Phaser game
            console.log('📋 Setting up game configuration...');
            const config = {
            type: Phaser.AUTO,
            width: GAME_CONFIG.width,
            height: GAME_CONFIG.height,
            parent: 'game-container',
            backgroundColor: GAME_CONFIG.backgroundColor,
            physics: GAME_CONFIG.physics,
            pixelArt: GAME_CONFIG.pixelArt,
            scene: [
                PreloadScene,
                HubScene,
                FrontendScene,
                BackendScene,
                CloudScene
            ],
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH,
                min: {
                    width: 800,
                    height: 450
                },
                max: {
                    width: 1600,
                    height: 900
                }
            },
            input: {
                keyboard: true,
                mouse: true,
                touch: true
            },
            render: {
                antialias: false,
                pixelArt: true,
                roundPixels: true,
                powerPreference: 'high-performance'
            },
            fps: {
                target: 60,
                forceSetTimeOut: true
            }
        };
        
            // Create game instance
            console.log('🎯 Creating Phaser game instance...');
            this.game = new Phaser.Game(config);

            // Store game reference globally
            window.game = this.game;

            // Setup game event listeners
            console.log('🔗 Setting up game events...');
            this.setupGameEvents();

            this.isInitialized = true;

            console.log('✅ Game initialized successfully!');

        } catch (error) {
            console.error('❌ Failed to initialize game:', error);
            throw error;
        }
    }
    
    setupGameEvents() {
        // Handle game ready
        this.game.events.on('ready', () => {
            console.log('Game is ready!');
            uiManager.handleResize();
        });
        
        // Handle scene transitions
        this.game.events.on('step', () => {
            // Update UI manager with current scene
            const activeScenes = this.game.scene.getScenes(true);
            if (activeScenes.length > 0) {
                uiManager.setCurrentScene(activeScenes[0].scene.key);
            }
        });
        
        // Handle visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Pause game when tab is not visible
                if (this.game && this.game.scene.isActive()) {
                    this.game.scene.pause();
                }
            } else {
                // Resume game when tab becomes visible
                if (this.game && this.game.scene.isPaused()) {
                    this.game.scene.resume();
                }
            }
        });
    }
    
    destroy() {
        if (this.game) {
            this.game.destroy(true);
            this.game = null;
            window.game = null;
            this.isInitialized = false;
        }
    }
    
    restart() {
        this.destroy();
        setTimeout(() => {
            this.init();
        }, 100);
    }
}

// Note: BackendScene and CloudScene are now implemented in separate files

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    console.log('🎮 DOM loaded, initializing game...');

    try {
        // Check dependencies
        if (typeof Phaser === 'undefined') {
            throw new Error('Phaser 3 not loaded');
        }
        console.log('✅ Phaser 3 loaded (version: ' + Phaser.VERSION + ')');

        if (typeof GAME_CONFIG === 'undefined') {
            throw new Error('GAME_CONFIG not loaded');
        }
        console.log('✅ Game config loaded');

        if (typeof PORTFOLIO_DATA === 'undefined') {
            throw new Error('PORTFOLIO_DATA not loaded');
        }
        console.log('✅ Portfolio data loaded');

        console.log('🚀 Creating game manager...');
        const gameManager = new GameManager();
        gameManager.init();

        // Store game manager globally for debugging
        window.gameManager = gameManager;
        console.log('✅ Game initialized successfully!');

    } catch (error) {
        console.error('❌ Failed to initialize game:', error.message);

        // Show error to user
        const gameContainer = document.getElementById('game-container');
        if (gameContainer) {
            gameContainer.innerHTML = `
                <div style="padding: 20px; color: red; text-align: center; font-family: monospace;">
                    <h2>Game Failed to Load</h2>
                    <p>Error: ${error.message}</p>
                    <p>Please check the browser console for more details.</p>
                    <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 10px;">Reload Page</button>
                </div>
            `;
        }
    }
});

// Handle page unload
window.addEventListener('beforeunload', () => {
    if (window.gameManager) {
        window.gameManager.destroy();
    }
});
